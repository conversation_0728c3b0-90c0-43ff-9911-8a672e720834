import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();

    const apiKey = request.headers['x-api-key'] || request.query['x-api-key'];
    const validKey = this.configService.get<string>(
      'CHATBOT_MICROSERVICE_API_KEY',
    );

    if (!validKey) {
      throw new Error('CHATBOT_MICROSERVICE_API_KEY is not defined');
    }

    if (apiKey !== validKey) {
      throw new UnauthorizedException('Invalid API Key');
    }
    const allowedIps = this.configService
      .get<string>('CHATBOT_ALLOWED_IPS')
      ?.split(',')
      .map((ip) => ip.trim());

    const requestIp =
      request.ip ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress;

    if (allowedIps && !allowedIps.includes(requestIp)) {
      throw new UnauthorizedException(`Access denied from IP: ${requestIp}`);
    }

    const userId = request.headers['x-user-id'] || request.query['x-user-id'];
    if (userId) {
      request.user = { _id: userId };
    }

    return true;
  }
}
