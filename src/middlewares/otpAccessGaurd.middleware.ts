import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { RouteAccessToken } from 'models/auth';

@Injectable()
export class OtpAccessGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    @InjectModel(RouteAccessToken.name)
    private readonly routeAccessTokenModel: Model<RouteAccessToken>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: Request = context.switchToHttp().getRequest();
    const otpAuthHeader = request.headers['otp-authorization'];

    if (
      !otpAuthHeader ||
      typeof otpAuthHeader !== 'string' ||
      !otpAuthHeader.startsWith('Bearer ')
    ) {
      throw new UnauthorizedException(
        'Please verify the email first before accessing this page',
      );
    }

    const otpToken = otpAuthHeader.split(' ')[1];

    try {
      // 🔐 Verify token signature and expiry
      const payload: any = this.jwtService.verify(otpToken, {
        secret: process.env.ROUTE_ACCESS_TOKEN_SECRET as string,
        algorithms: ['HS512'],
      });

      // 🔍 Check token in DB
      const dbToken = await this.routeAccessTokenModel.findOne({
        token: otpToken,
        isExpired: false,
      });

      if (!dbToken) {
        throw new UnauthorizedException(
          'OTP access token is invalid or expired',
        );
      }

      if (dbToken.expiry.getTime() < Date.now()) {
        dbToken.isExpired = true;
        await dbToken.save();
        throw new UnauthorizedException('OTP access token has expired');
      }

      // ✅ Attach admin info to request
      request['otpAdmin'] = payload.user;

      return true;
    } catch {
      throw new UnauthorizedException('Invalid or expired OTP access token');
    }
  }
}
