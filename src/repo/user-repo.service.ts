import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Model } from 'mongoose';
import { EncryptionService } from 'src/common/services';
import { RedisCacheService } from 'src/redis/redis.service';

@Injectable()
export class UserRepoService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    private encryptionService: EncryptionService,
    private redisCacheService: RedisCacheService,
  ) {}

  async findUserByEmail(
    email: string,
    caching: boolean = true,
  ): Promise<User | null> {
    const encryptedEmail = this.encryptionService.encrypt(email);
    const cacheKey = `user:email:${encryptedEmail}`;

    if (caching) {
      try {
        const cachedUser = await this.redisCacheService.get<User>(cacheKey);
        if (cachedUser) return cachedUser;
      } catch (error) {
        console.error('Redis Cache Error:', error);
      }
    }

    const user = await this.userModel
      .findOne({ email: encryptedEmail })
      .select('+password')
      .exec();

    if (user) {
      user.email = this.encryptionService.decrypt(user.email);

      if (caching) {
        try {
          await this.redisCacheService.set(cacheKey, user, 3600);
        } catch (error) {
          console.error('Redis Cache Set Error:', error);
        }
      }
    }

    return user;
  }

  async findUserById(userId: string): Promise<User | null> {
    const cacheKey = `user:id:${userId}`;

    // Check if user is in cache
    const cachedUser = await this.redisCacheService.get<User>(cacheKey);
    if (cachedUser) return cachedUser;

    const user = await this.userModel.findById(userId).exec();
    if (user) {
      user.email = this.encryptionService.decrypt(user.email);

      // Cache decrypted email
      await this.redisCacheService.set(cacheKey, user, 3600);
    }
    return user;
  }

  async findUserByIdAndUpdate(
    userId: string,
    updateData: any,
  ): Promise<User | null> {
    const cacheKey = `user:id:${userId}`;

    // Check if user is in cache
    await this.redisCacheService.delete(cacheKey);

    if (updateData.email) {
      updateData.email = this.encryptionService.encrypt(updateData.email);
    }

    const user = await this.userModel
      .findByIdAndUpdate(userId, updateData, {
        new: true,
        runValidators: true,
      })
      .populate({
        path: 'app_permissions',
        model: 'AppPermission',
      })
      .exec();

    if (user) {
      user.email = this.encryptionService.decrypt(user.email);

      // Cache decrypted email
      await this.redisCacheService.set(cacheKey, user, 3600);
    }

    return user;
  }
}
