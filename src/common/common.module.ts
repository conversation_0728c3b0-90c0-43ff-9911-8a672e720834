import { Module } from '@nestjs/common';
import {
  CustomConfigService,
  CustomLogger,
  EmailService,
  EncryptionService,
  MicroserviceHttpClientService,
  UserJourneyService,
  UtilsService,
  OtpService,
} from './services';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { MongooseModule } from '@nestjs/mongoose';
import {
  UserJourneyPerDay,
  UserJourneyPerDaySchema,
} from 'models/user-journey/user-journey.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserJourneyPerDay.name, schema: UserJourneyPerDaySchema },
    ]),
    ThirdPartyModule,
    ConfigModule,
    HttpModule,
  ],
  providers: [
    EmailService,
    CustomLogger,
    CustomConfigService,
    EncryptionService,
    UtilsService,
    MicroserviceHttpClientService,
    UserJourneyService,
    OtpService,
  ],
  exports: [
    EmailService,
    CustomLogger,
    CustomConfigService,
    EncryptionService,
    UtilsService,
    MicroserviceHttpClientService,
    UserJourneyService,
  ],
})
export class CommonModule {}
