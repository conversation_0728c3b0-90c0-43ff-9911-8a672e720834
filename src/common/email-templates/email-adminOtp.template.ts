export const AdminOtpEmailTemplate = `<html>
  <head>
    <style>
      body {
        font-family: "Exo", serif;
        font-optical-sizing: auto;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        border-radius: 10px;
        border: 1px solid #ddd;
        background-color: #387160;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        border-bottom: 2px solid #fff;
        padding: 18px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
      .header img {
        width: 169px;
        height: 5vh;
      }
      h1 {
        color: white;
        font-size: 24px;
        margin-bottom: 20px;
      }
      .message {
        font-size: 16px;
        color: white;
        margin-bottom: 20px;
        line-height: 1.6;
      }
      .otp-box {
        display: flex;
        font-size: 24px;
        color: #3f2a66;
        font-weight: bold;
        letter-spacing: 2px;
        text-align: center;

      }
      .footer {
        text-align: center;
        margin-top: 30px;
        color: white;
        font-size: 14px;
        line-height: 1.5;
      }
      .main {
        padding: 20px;
      }
    </style>

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Exo:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
  </head>

  <body>
    <div class="container">
      <div class="header">
        <h2>Appetec</h2>
      </div>
      <div class="main">
        <h1>Hello, <span style="color: white">$$email</span></h1>

        <p class="message">
          You're attempting to access a secure section of the Appetec Admin Panel.<br />
          To verify it's really you, use the one-time passcode (OTP) shown below:
        </p>

        <div class="otp-box">$$otp</div>

        <p class="message">
          For your protection, this code will expire in <strong>$$timeLeftMessage</strong>.<br />
          If you didn’t try to access this resource, please disregard this email.
        </p>

        <p class="message">
          Stay secure,<br />
          <strong>The Appetec Team</strong>
        </p>

        <p class="footer">
          This is an automated message sent to <em>authorized administrators</em> of the Appetec system.<br />
          &copy; ${new Date().getFullYear()} Appetec. All rights reserved.
        </p>
      </div>
    </div>
  </body>
</html>`;
