import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';

@Injectable()
export class UtilsService {
  parsePageNumberAndGetlimitAndOffset(
    page: string = '1',
    limit: number = 40,
  ): {
    offset: number;
    limit: number;
  } {
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const offset = (pageNumber - 1) * limit;

    return {
      offset,
      limit,
    };
  }

  ensureObjectId(id: string | Types.ObjectId): Types.ObjectId {
    return typeof id === 'string' ? new Types.ObjectId(id) : id;
  }
}
