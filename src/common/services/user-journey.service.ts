import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { UserJourneyPerDay } from 'models/user-journey';
import { Model } from 'mongoose';
import * as moment from 'moment-timezone';

@Injectable()
export class UserJourneyService {
  constructor(
    @InjectModel(UserJourneyPerDay.name)
    private readonly userJourneyModel: Model<UserJourneyPerDay>,
  ) {}

  async ensureUserJourney(
    userId: string,
    timeZone: string,
    age: number,
  ): Promise<void> {
    const today = new Date();
    const UTCDate = moment.tz(today, timeZone).utc().toDate();
    const currentDate = new Date(UTCDate.setHours(0, 0, 0, 0));

    await this.userJourneyModel.findOneAndUpdate(
      { userId, time: currentDate },
      {
        $set: { age },
        $setOnInsert: { time: currentDate },
      },
      { upsert: true, new: true },
    );

    return;
  }
}
