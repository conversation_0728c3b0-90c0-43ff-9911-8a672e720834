import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as CryptoJS from 'crypto-js';

@Injectable()
export class EncryptionService {
  private readonly encryptionKey: string;
  private readonly salt: string;
  private readonly iv_string: string;
  private readonly iv: CryptoJS.lib.WordArray;
  private readonly keyWithSalt: CryptoJS.lib.WordArray;

  constructor(private configService: ConfigService) {
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY');
    this.salt = this.configService.get<string>('ENCRYPTION_SALT');
    this.iv_string = this.configService.get<string>('ENCRYPTION_IV');

    // Precompute key derivation
    this.keyWithSalt = CryptoJS.PBKDF2(
      this.encryptionKey + this.salt,
      this.salt,
      { keySize: 256 / 32 },
    );

    // Precompute IV (must be 16 bytes)
    this.iv = CryptoJS.enc.Utf8.parse(this.iv_string); // Replace with a secure IV
  }

  encrypt(text: string): string {
    const encrypted = CryptoJS.AES.encrypt(text, this.keyWithSalt, {
      iv: this.iv,
    });
    return encrypted.toString();
  }

  decrypt(encryptedText: string): string {
    const decryptedBytes = CryptoJS.AES.decrypt(
      encryptedText,
      this.keyWithSalt,
      { iv: this.iv },
    );
    return decryptedBytes.toString(CryptoJS.enc.Utf8);
  }
}
