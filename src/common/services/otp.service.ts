import { Injectable } from '@nestjs/common';

const otpStore = new Map<
  string,
  { otp: string; expiresAt: number; email: string }
>();

@Injectable()
export class OtpService {
  generateOtp(email: string): string {
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes
    otpStore.set(otp, { otp, expiresAt, email });
    return otp;
  }

  verifyOtp(otp: string): string | null {
    const record = otpStore.get(otp);
    if (!record || record.expiresAt < Date.now()) {
      return null;
    }
    otpStore.delete(otp); // One-time use
    return record.email;
  }

  async sendOtpEmail(email: string, otp: string): Promise<void> {
    console.log(`Send email to ${email} with OTP: ${otp}`);
    // Or use nodemailer/SendGrid/SES here
  }
}
