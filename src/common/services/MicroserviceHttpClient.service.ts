import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  HttpMicroserviceGetResInterface,
  HttpMicroservicePostResInterface,
} from '../interfaces';

@Injectable()
export class MicroserviceHttpClientService {
  constructor(private readonly httpService: HttpService) {}

  private getHeaders(apiKey: string) {
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${apiKey}`,
    };
  }

  async get(
    apiKey: string,
    url: string,
    params?: any,
  ): Promise<HttpMicroserviceGetResInterface> {
    try {
      const resp = await lastValueFrom(
        this.httpService
          .get(url, {
            params,
            headers: this.getHeaders(apiKey),
          })
          .pipe(map((response) => response.data)),
      );

      return { error: false, resp };
    } catch (error) {
      return {
        error: true,
        resp: error?.response || 'Something went wrong, Try again later !!',
      };
    }
  }

  async post(
    apiKey: string,
    url: string,
    data: any,
  ): Promise<HttpMicroservicePostResInterface> {
    try {
      const resp = await lastValueFrom(
        this.httpService
          .post(url, data, {
            headers: this.getHeaders(apiKey),
          })
          .pipe(map((response) => response.data)),
      );

      return { error: false, resp };
    } catch (error) {
      return {
        error: true,
        resp: error?.response || 'Something went wrong, Try again later !!',
      };
    }
  }
  async put(
    apiKey: string,
    url: string,
    data: any,
  ): Promise<HttpMicroservicePostResInterface> {
    try {
      const resp = await lastValueFrom(
        this.httpService
          .put(url, data, {
            headers: this.getHeaders(apiKey),
          })
          .pipe(map((response) => response.data)),
      );

      return { error: false, resp };
    } catch (error) {
      return {
        error: true,
        resp: error?.response || 'Something went wrong, Try again later !!',
      };
    }
  }
}
