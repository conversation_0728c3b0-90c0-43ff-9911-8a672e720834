import { Module } from '@nestjs/common';
import { CredentialsAuthService } from './credentials-auth/credentials-auth.service';
import { CredentialsAuthController } from './credentials-auth/credentials-auth.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AppPermission,
  AppPermissionSchema,
  User,
  UserSchema,
} from 'models/user';
import { CommonModule } from 'src/common/common.module';
import {
  AccessToken,
  AccessTokenSchema,
  RefreshToken,
  RefreshTokenSchema,
  VerificationToken,
  VerificationTokenSchema,
} from 'models/auth';
import { JwtModule } from '@nestjs/jwt';
import { RepoModule } from 'src/repo/repo.module';
import {
  NotificationMessage,
  NotificationMessageSchema,
  NotificationToken,
  NotificationTokenSchema,
} from 'models/notificationToken';
import { RemindersService } from 'src/user/reminders/reminders.service';
import { RemindersUtilsService } from 'src/user/reminders/reminders-utils.service';
import {
  ReminderCategory,
  ReminderCategorySchema,
  ReminderSettings,
  RemindersSettingsSchema,
} from 'models/reminders';
import { Recipes, RecipesSchema } from 'models/recipe';
import { UserBootstrapService } from './credentials-auth/user-register-bootstrap.service';
import { CredentialsAuthUtilsService } from './credentials-auth/credentials-auth-utils.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: VerificationToken.name, schema: VerificationTokenSchema },
      { name: AccessToken.name, schema: AccessTokenSchema },
      { name: RefreshToken.name, schema: RefreshTokenSchema },
      { name: AppPermission.name, schema: AppPermissionSchema },
      { name: NotificationToken.name, schema: NotificationTokenSchema },
      { name: NotificationMessage.name, schema: NotificationMessageSchema },
      { name: ReminderSettings.name, schema: RemindersSettingsSchema },
      { name: ReminderCategory.name, schema: ReminderCategorySchema },
      { name: Recipes.name, schema: RecipesSchema },
    ]),
    JwtModule.register({}),
    CommonModule,
    RepoModule,
  ],
  controllers: [CredentialsAuthController],
  providers: [
    CredentialsAuthService,
    CredentialsAuthUtilsService,
    UserBootstrapService,
    RemindersService,
    RemindersUtilsService,
  ],
  exports: [MongooseModule, CredentialsAuthUtilsService],
})
export class AuthModule {}
