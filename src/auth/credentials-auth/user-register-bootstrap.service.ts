import { Injectable } from '@nestjs/common';
import { User } from 'models/user';
import { CreateReminderReqDTO } from 'src/user/reminders/dto';
import { REMINDER_CATEGORY_ENUM } from 'src/user/reminders/reminders-utils.service';
import { RemindersService } from 'src/user/reminders/reminders.service';
import { fromZonedTime } from 'date-fns-tz';

@Injectable()
export class UserBootstrapService {
  constructor(private readonly remindersService: RemindersService) {}

  async createUserBootstrapReminders(user: User) {
    const today = new Date();

    const BootStrapReminderData = [
      {
        category: REMINDER_CATEGORY_ENUM.FOOD,
        frontend_screen_url: 'appetec://reminders',
        label: 'Breakfast',
        hour: 9,
        minute: 0,
      },
      {
        category: REMINDER_CATEGORY_ENUM.FOOD,
        frontend_screen_url: 'appetec://reminders',
        label: 'Lunch',
        hour: 13, // 1 PM
        minute: 0,
      },
      {
        category: REMINDER_CATEGORY_ENUM.FOOD,
        frontend_screen_url: 'appetec://reminders',
        label: 'Dinner',
        hour: 19, // 7 PM
        minute: 0,
      },
      {
        category: REMINDER_CATEGORY_ENUM.WORKOUT,
        frontend_screen_url: 'appetec://reminders',
        label: 'Walking',
        hour: 7,
        minute: 0,
      },
    ];

    await Promise.all(
      BootStrapReminderData.map(async (single) => {
        const timeZone = user.timeZone || 'UTC';

        const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}T${String(single.hour).padStart(2, '0')}:${String(single.minute).padStart(2, '0')}:00`;

        const reminderTime = fromZonedTime(dateStr, timeZone);

        const reminderData: CreateReminderReqDTO = {
          category: single.category,
          frontend_screen_url: single.frontend_screen_url,
          label: single.label,
          sound: {
            android: 'default',
            ios: 'default',
          },
          time: reminderTime,
        };

        await this.remindersService.createReminder(
          reminderData,
          user._id.toString(),
        );
      }),
    );
  }
}
