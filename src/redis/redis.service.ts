import { Injectable } from '@nestjs/common';
import { RedisConfigService } from 'config';
import { CustomLogger } from 'src/common/services';

@Injectable()
export class RedisCacheService {
  private readonly defaultTTL = 3600; // 1 hour in seconds

  constructor(
    private readonly redisConfigService: RedisConfigService,
    private readonly logger: CustomLogger,
  ) {}

  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await this.redisConfigService
          .getClient()
          .set(key, serializedValue, 'EX', ttl);
      } else {
        await this.redisConfigService
          .getClient()
          .set(key, serializedValue, 'EX', this.defaultTTL);
      }
      this.logger.log(`Cache set for key: ${key}`);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const data = await this.redisConfigService.getClient().get(key);
      if (!data) return null;

      return JSON.parse(data) as T;
    } catch (error) {
      this.logger.error(error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.redisConfigService.getClient().del(key);
      this.logger.log(`Cache deleted for key: ${key}`);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async deletePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redisConfigService.getClient().keys(pattern);
      if (keys.length > 0) {
        await this.redisConfigService.getClient().del(keys);
        this.logger.log(
          `Deleted ${keys.length} keys matching pattern: ${pattern}`,
        );
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const exists = await this.redisConfigService.getClient().exists(key);
      return exists === 1;
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getTTL(key: string): Promise<number> {
    try {
      return await this.redisConfigService.getClient().ttl(key);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async clearAll(): Promise<void> {
    try {
      await this.redisConfigService.getClient().flushall();
      this.logger.log('Cache cleared completely');
    } catch (error) {
      this.logger.error(error);
    }
  }
}
