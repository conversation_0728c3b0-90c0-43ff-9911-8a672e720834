import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import OpenAI from 'openai';

@Injectable()
export class OpenAIService {
  private readonly logger = new Logger(OpenAIService.name);
  private openAIClient: OpenAI;

  constructor() {
    this.openAIClient = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  // Method to generate a response from OpenAI based on a prompt
  async generateResponse(prompt: string): Promise<string> {
    try {
      const response = await this.openAIClient.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        // max_tokens: 1,
      });

      return response.choices[0].message.content;
    } catch (error) {
      this.logger.error(`OpenAI API call failed: ${error.message}`);
      throw new BadRequestException('Failed to generate response from AI.');
    }
  }
}
