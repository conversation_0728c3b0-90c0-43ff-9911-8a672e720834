import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Stomp from 'stomp-client';

@Injectable()
export class ActiveMQService implements OnModuleDestroy {
  private client: any;
  private subscriptions: { [key: string]: any } = {};

  constructor(private readonly configService: ConfigService) {
    this.initializeClient();
  }

  private initializeClient() {
    const host = this.configService.get('ACTIVEMQ_HOST') || 'localhost';
    const port = this.configService.get('ACTIVEMQ_PORT') || 61613;
    const user = this.configService.get('ACTIVEMQ_USERNAME') || 'admin';
    const pass = this.configService.get('ACTIVEMQ_PASSWORD') || 'admin';

    this.client = new Stomp(host, port, user, pass);

    this.client.on('connect', () => {
      console.log('[ActiveMQ] Connected to ActiveMQ via STOMP');
    });

    this.client.on('error', (error) => {
      console.error('[ActiveMQ] STOMP Error:', error);
    });

    this.client.connect(() => {
      console.log('[ActiveMQ] Connection established');
    });
  }

  sendMessageWithDelay(queue: string, message: any, delayMs: number) {
    const headers = {
      AMQ_SCHEDULED_DELAY: delayMs,
      persistent: 'true',
    };

    this.client.publish(
      `/queue/${queue}`,
      JSON.stringify(message),
      headers,
      (err: any) => {
        if (err) {
          console.error('[ActiveMQ] Message publish error:', err);
        }
      },
    );
  }

  subscribeToQueue(queue: string, callback: (message: any) => void) {
    const destination = `/queue/${queue}`;

    this.subscriptions[queue] = this.client.subscribe(
      destination,
      (body: string) => {
        try {
          const message = typeof body === 'string' ? JSON.parse(body) : body;
          callback(message);
        } catch (e) {
          console.error(
            `[ActiveMQ] Failed to parse message for queue [${queue}]:`,
            e,
          );
        }
      },
    );

    console.log(`[ActiveMQ] Subscribed to queue: ${queue}`);
  }

  onModuleDestroy() {
    Object.values(this.subscriptions).forEach((sub) => sub.unsubscribe());
    this.client.disconnect();
  }
}
