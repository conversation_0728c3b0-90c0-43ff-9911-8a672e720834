import { Injectable, Logger } from '@nestjs/common';
import { ChatOpenAI } from '@langchain/openai';
// import { ChatOpenAI } from 'langchain/chat_models/openai';
import { UserDTO } from './dto';
import { ConfigService } from '@nestjs/config';
import OpenAI, { APIError } from 'openai';
import { BaseMessage } from '@langchain/core/messages';
import { Chat_Session } from 'src/models/chat';
import { HealthDataService } from './health-data/health-data.service';
import { ChatbotRedisService } from './cache-service/chatbot.redis.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

export const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

@Injectable()
export class ChatbotUtilsService {
  private currentKeyIndex = 0;
  private readonly logger = new Logger(ChatbotUtilsService.name);
  constructor(
    @InjectModel(Chat_Session.name) private sessionModel: Model<Chat_Session>,
    private readonly configService: ConfigService,
    private readonly healthDataService: HealthDataService,
    private readonly chatbotRedisService: ChatbotRedisService,
  ) {}

  getAdminContactInfo() {
    const email = this.configService.get<string>('ADMIN_EMAIL_INFO') || '';
    const phone = this.configService.get<string>('ADMIN_PHONE_INFO') || '';
    return { email, phone };
  }

  // async askWithFallback(
  //   messages: BaseMessage[],
  //   model = 'gpt-4o',
  // ): Promise<{ content: string | null; keyIndex: number }> {
  //   const rawKeys = this.configService.get<string>('OPENAI_API_KEY') || '';
  //   const keys = rawKeys
  //     .split(',')
  //     .map((k) => k.trim())
  //     .filter(Boolean);
  //   if (!keys.length) throw new Error('No OpenAI API keys configured');

  //   const totalKeys = keys.length;
  //   const startIndex = this.currentKeyIndex;
  //   this.currentKeyIndex = (this.currentKeyIndex + 1) % totalKeys;

  //   for (let i = 0; i < totalKeys; i++) {
  //     const index = (startIndex + i) % totalKeys;
  //     const apiKey = keys[index];
  //     const llm = new ChatOpenAI({ apiKey, model, temperature: 0 });

  //     this.logger.log(`Trying OpenAI key index ${index}`);

  //     try {
  //       const res = await llm.invoke(messages);
  //       if (typeof res.content === 'string') {
  //         this.logger.log(`Successfully used key index ${index}`);
  //         return { content: res.content, keyIndex: index };
  //       }
  //     } catch (err: any) {
  //       this.logger.warn(`OpenAI failed at index ${index}: ${err.message}`);
  //       this.logger.debug(
  //         `Full error for index ${index}: ${JSON.stringify(err)}`,
  //       );
  //       continue;
  //     }
  //   }

  //   throw new Error('All OpenAI API keys failed');
  // }

  // async askWithKeyIndex(
  //   messages: BaseMessage[],
  //   keyIndex: number,
  //   model = 'gpt-4o',
  // ): Promise<{ content: string | null }> {
  //   const rawKeys = this.configService.get<string>('OPENAI_API_KEY') || '';
  //   const keys = rawKeys
  //     .split(',')
  //     .map((k) => k.trim())
  //     .filter(Boolean);

  //   if (!keys[keyIndex]) throw new Error(`Invalid key index ${keyIndex}`);

  //   const apiKey = keys[keyIndex];
  //   const llm = new ChatOpenAI({ apiKey, model, temperature: 0 });

  //   const res = await llm.invoke(messages);
  //   return {
  //     content:
  //       typeof res.content === 'string'
  //         ? res.content
  //         : res.content
  //           ? JSON.stringify(res.content)
  //           : null,
  //   };
  // }
  async askWithFallback(
    messages: BaseMessage[],
    model = 'gpt-4o', // Azure deployment name
  ): Promise<{ content: string | null; keyIndex: number }> {
    const rawKeys = this.configService.get<string>('AZURE_OPENAI_KEYS') || '';
    const keys = rawKeys
      .split(',')
      .map((k) => k.trim())
      .filter(Boolean);
    if (!keys.length) throw new Error('No Azure OpenAI API keys configured');

    const totalKeys = keys.length;
    const startIndex = this.currentKeyIndex;
    this.currentKeyIndex = (this.currentKeyIndex + 1) % totalKeys;

    for (let i = 0; i < totalKeys; i++) {
      const index = (startIndex + i) % totalKeys;
      const apiKey = keys[index];

      const llm = new ChatOpenAI({
        temperature: 0,
        modelName: 'gpt-4o',
        apiKey: apiKey,
      });

      this.logger.log(`Trying Azure key index ${index}`);

      try {
        const res = await llm.invoke(messages);
        if (typeof res.content === 'string') {
          this.logger.log(`Successfully used Azure key index ${index}`);
          return { content: res.content, keyIndex: index };
        }
      } catch (err: any) {
        this.logger.warn(
          `Azure OpenAI failed at index ${index}: ${err.message}`,
        );
        this.logger.debug(
          `Full error for index ${index}: ${JSON.stringify(err)}`,
        );
        continue;
      }
    }

    throw new Error('All Azure OpenAI keys failed');
  }

  async askWithKeyIndex(
    messages: BaseMessage[],
    keyIndex: number,
    model = 'gpt-4o',
  ): Promise<{ content: string | null }> {
    const rawKeys = this.configService.get<string>('OPENAI_API_KEY') || '';
    const keys = rawKeys
      .split(',')
      .map((k) => k.trim())
      .filter(Boolean);

    if (!keys[keyIndex]) throw new Error(`Invalid key index ${keyIndex}`);

    const apiKey = keys[keyIndex];
    const llm = new ChatOpenAI({ apiKey, model, temperature: 0 });

    const res = await llm.invoke(messages);
    return {
      content:
        typeof res.content === 'string'
          ? res.content
          : res.content
            ? JSON.stringify(res.content)
            : null,
    };
  }

  stripMarkdownBold(text: string): string {
    return text.replace(/\*\*(.*?)\*\*/g, '$1');
  }

  resolveRelativeDates(question: string): string {
    const now = new Date();
    const today = new Date(now);
    const yesterday = new Date(now);
    yesterday.setDate(today.getDate() - 1);

    const format = (date: Date) => date.toISOString().split('T')[0];

    return question
      .replace(/\btoday\b/gi, format(today))
      .replace(/\byesterday\b/gi, format(yesterday));
  }

  buildPrompt(
    template: string,
    user: UserDTO,
    healthData: any,
    // chatHistory: any[],
    question: string,
    userMenuData: any,
    attachmentPrompt: string = '',
    adminContactInfo: { email: string; phone: string },
  ): string {
    const serializedDump = healthData?.dump
      ? {
          manualData: healthData.dump.manualData?.slice(0, 10) ?? [],
          systemData: healthData.dump.systemData?.slice(0, 10) ?? [],
          ledgerData: healthData.dump.ledgerData?.slice(0, 10) ?? [],
        }
      : {};

    const serializedMicroservice = healthData?.microservice?.data
      ? healthData.microservice.data.map((entry) => ({
          name: entry.name,
          measurements: entry.measurements,
          time: entry.time,
        }))
      : [];

    const serializedMenuData = {
      deviceUsage: userMenuData?.userDeviceRecordData?.slice(-5) || [],
      moodRecords: userMenuData?.userMoodRecordData?.slice(-5) || [],
      mealRecords: userMenuData?.userMealRecordData?.slice(-5) || [],
      weightSummary: userMenuData?.monthlyWeightSummaryData?.slice(-3) || [],
      sleepRecords: userMenuData?.userSleepRecords?.slice(-5) || [],
    };

    const serializedManualData = (healthData?.dump?.manualData ?? [])
      .slice(0, 10)
      .map((entry) => ({
        activityType: entry.activityType,
        durationInMinutes: entry.durationInMinutes,
        steps: entry.steps,
        burnedCalories: entry.burnedCalories,
        localDate: entry.localDate,
      }));

    const currentDate = new Date().toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });

    return template
      .replace('${user.age}', String(user.age ?? 'N/A'))
      .replace('${user.height}', String(user.height ?? 'N/A'))
      .replace('${user.weight}', String(user.weight ?? 'N/A'))
      .replace(
        '${JSON.stringify(user.goals)}',
        JSON.stringify(user.goals ?? []),
      )
      .replace(
        '${JSON.stringify(healthData.microservice)}',
        JSON.stringify(serializedMicroservice),
      )
      .replace(
        '${JSON.stringify(healthData.dump)}',
        JSON.stringify(serializedDump),
      )
      .replace('${manualData}', JSON.stringify(serializedManualData))
      .replace('${attachmentPrompt}', attachmentPrompt)
      .replace(
        '${mealRecords}',
        JSON.stringify(serializedMenuData.mealRecords ?? []),
      )
      .replace(
        '${moodRecords}',
        JSON.stringify(serializedMenuData.moodRecords ?? []),
      )
      .replace(
        '${sleepRecords}',
        JSON.stringify(serializedMenuData.sleepRecords ?? []),
      )
      .replace(
        '${deviceUsage}',
        JSON.stringify(serializedMenuData.deviceUsage ?? []),
      )
      .replace(
        '${weightSummary}',
        JSON.stringify(serializedMenuData.weightSummary ?? []),
      )
      .replace('${adminContactInfo}', JSON.stringify(adminContactInfo))
      .replace('${question}', question)
      .replace('${currentDate}', currentDate);
  }

  buildLLMMessages(prompt: string, systemPrompt: string, chatHistory: any[]) {
    return [
      { role: 'system', content: systemPrompt },
      ...chatHistory,
      { role: 'user', content: prompt },
    ];
  }

  async createNewSession(user: UserDTO): Promise<Chat_Session> {
    const healthData = await this.healthDataService.findUserhealthData(user);

    const expiryDurationRaw = this.configService.get<string>(
      'CHATBOT_SESSION_EXPIRY',
    );
    const expiryDurationMs = parseInt(expiryDurationRaw || '', 10);
    const createdAt = new Date();
    const expiredAt = new Date(createdAt.getTime() + expiryDurationMs);
    const session = await this.sessionModel.create({
      userId: user._id,
      healthDataId: healthData._id,
      isExpired: false,
      createdAt,
      expiredAt,
    });

    await this.chatbotRedisService.clearAllUserSessionCaches(user._id);
    return session;
  }

  extractLinkFromReply(reply: string): {
    reply: string;
    link?: { name: string; url: string };
  } {
    const regex = /\[([^\]]+)\]\((appetec:\/\/[^\)]+)\)/;
    const match = reply.match(regex);

    if (match) {
      const name = match[1];
      const url = match[2];
      let cleanReply = reply.replace(regex, '').trim();
      const trailingLabelRegex = new RegExp(`${name}\\.?$`, 'i');
      cleanReply = cleanReply.replace(trailingLabelRegex, '').trim();
      cleanReply = cleanReply.replace(/\s+([.,!?])/, '$1');
      if (!cleanReply.endsWith('.')) {
        cleanReply += '';
      }

      return {
        reply: cleanReply,
        link: { name, url },
      };
    }

    return { reply };
  }
}
