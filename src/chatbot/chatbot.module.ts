import { Module } from '@nestjs/common';
import { ChatbotController } from './chatbot.controller';
import { ChatbotService } from './chatbot.service';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { Faq, FaqSchema } from 'models/faq';
import { Help, HelpSchema } from 'models/help';
import { MongooseModule } from '@nestjs/mongoose';
import { RedisCacheService } from 'src/redis/redis.service';
import { CustomLogger } from 'src/common/services';
import { RedisConfigService } from 'config';
import { HealthModule } from './health/health.module';
import { FileUploadModule } from 'src/user/file-upload/file-upload.module';

@Module({
  imports: [
    CommonModule,
    RepoModule,
    AuthModule,
    ConfigModule,
    MongooseModule.forFeature([{ name: Faq.name, schema: FaqSchema }]),
    MongooseModule.forFeature([{ name: Help.name, schema: HelpSchema }]),
    HealthModule,
    FileUploadModule,
  ],
  controllers: [ChatbotController],
  providers: [
    ChatbotService,
    RedisCacheService,
    CustomLogger,
    RedisConfigService,
  ],
})
export class ChatbotModule {}
