import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { UserDTO } from './user.dto';

export class AttachmentMetaDTO {
  @ApiProperty()
  @IsString()
  fileId: string;

  @ApiProperty()
  @IsString()
  url: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  mimetype: string;

  @ApiProperty()
  @IsString()
  size: number;

  @ApiProperty()
  @IsString()
  type: string; // image, audio, video, document
}

export class ChatBotReqDTO {
  @ApiPropertyOptional({
    description:
      'Session ID to continue an existing conversation. A new session will be created if not provided.',
    type: String,
  })
  @IsString()
  @IsOptional()
  sessionId?: string;

  @ApiProperty({
    description: 'User details required to personalize the chatbot experience.',
    type: UserDTO,
  })
  @ValidateNested()
  @Type(() => UserDTO)
  @IsObject()
  @IsNotEmpty()
  user: UserDTO;

  @ApiProperty({
    description: "The user's question or input to the chatbot.",
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiPropertyOptional({ type: AttachmentMetaDTO })
  @ValidateNested()
  @Type(() => AttachmentMetaDTO)
  @IsOptional()
  attachment?: AttachmentMetaDTO;
}
