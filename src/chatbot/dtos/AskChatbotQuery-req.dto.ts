import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class AskChatbotQueryReqDTO {
  @ApiProperty({
    description: 'The question to ask the chatbot.',
    example: 'What are some healthy breakfast ideas?',
  })
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({
    description: 'The session id.',
    example: '1346546554616161',
  })
  @IsString()
  @IsOptional()
  sessionId?: string;
}
