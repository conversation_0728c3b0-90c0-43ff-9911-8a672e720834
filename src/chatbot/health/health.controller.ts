import {
  Controller,
  Get,
  UseGuards,
  Req,
  BadRequestException,
} from '@nestjs/common';
import { Request } from 'express';
// import { AuthGuard } from 'src/middlewares';
import { ApiKeyGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { HealthService } from './health.service';

@ApiTags('Health-Device')
@ApiBearerAuth()
@UseGuards(ApiKeyGuard)
@Controller('health-device')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @ApiResponse({
    status: 200,
    description: 'Fetched Latest Health data Successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseGuards(ApiKeyGuard)
  @Get('encrypted-data')
  async getEncryptedHealthData(@Req() req: Request) {
    let user = req['user'];

    // If APIKeyGuard didn't attach `user`, fallback to query param
    if (!user || !user._id) {
      const userId = req.query['x-user-id'];
      if (!userId) {
        throw new BadRequestException('Missing userId');
      }
      user = { _id: userId };
    }

    return this.healthService.getUserHealthRecords(user._id, user.timeZone);
  }
}
