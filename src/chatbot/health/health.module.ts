import { Module } from '@nestjs/common';
import { HealthService } from './health.service';
import { HealthController } from './health.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  UserDeviceRecord,
  UserDeviceRecordSchema,
} from 'models/user-records/Device-records';
import { UserGoal, UserGoalSchema } from 'models/user';
import {
  UserSleepRecords,
  UserSleepRecordsSchema,
} from 'models/user-records/Sleep-Records';
import {
  UserMoodRecords,
  UserMoodRecordsSchema,
} from 'models/user-records/Mood-Records';
import {
  UserMealRecords,
  UserMealRecordsSchema,
} from 'models/user-records/Meal-Records';
import {
  MonthlyWeightSummary,
  MonthlyWeightSummarySchema,
} from 'models/user-records/Weight-Records';
import { HttpModule } from '@nestjs/axios';
import { CustomLogger, DataEncryptionService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { UserJourneyService } from 'src/common/services';
import { CommonModule } from 'src/common/common.module';
import {
  UserJourneyPerDay,
  UserJourneyPerDaySchema,
} from 'models/user-journey';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserDeviceRecord.name, schema: UserDeviceRecordSchema },
      { name: UserGoal.name, schema: UserGoalSchema },
      { name: UserSleepRecords.name, schema: UserSleepRecordsSchema },
      { name: UserMoodRecords.name, schema: UserMoodRecordsSchema },
      { name: UserMealRecords.name, schema: UserMealRecordsSchema },
      { name: MonthlyWeightSummary.name, schema: MonthlyWeightSummarySchema },
      { name: UserJourneyPerDay.name, schema: UserJourneyPerDaySchema },
    ]),
    HttpModule,
    RepoModule,
    AuthModule,
    CommonModule,
  ],
  providers: [
    HealthService,
    DataEncryptionService,
    ConfigService,
    CustomLogger,
    UserJourneyService,
  ],
  controllers: [HealthController],
})
export class HealthModule {}
