import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { UserDeviceRecord } from 'models/user-records/Device-records';
import { UserGoal } from 'models/user';
import { Model } from 'mongoose';
import { UserSleepRecords } from 'models/user-records/Sleep-Records';
import { UserMoodRecords } from 'models/user-records/Mood-Records';
import { UserMealRecords } from 'models/user-records/Meal-Records';
import { MonthlyWeightSummary } from 'models/user-records/Weight-Records';
import * as moment from 'moment';
import { DataEncryptionService } from 'src/common/services';
@Injectable()
export class HealthService {
  constructor(
    @InjectModel(UserDeviceRecord.name)
    private userDeviceRecord: Model<UserDeviceRecord>,
    @InjectModel(UserGoal.name)
    private userGoal: Model<UserGoal>,
    @InjectModel(UserMoodRecords.name)
    private userMoodRecords: Model<UserMoodRecords>,
    @InjectModel(UserMealRecords.name)
    private userMealRecords: Model<UserMealRecords>,
    @InjectModel(MonthlyWeightSummary.name)
    private monthlyWeightSummary: Model<MonthlyWeightSummary>,
    @InjectModel(UserSleepRecords.name)
    private userSleepRecords: Model<UserSleepRecords>,
    private readonly dataEncryptionService: DataEncryptionService,
  ) {}

  async getUserHealthRecords(userId, timeZone) {
    const startDate = moment
      .tz(timeZone)
      .startOf('day')
      .subtract(6, 'days')
      .toDate();
    const endDate = moment.tz(timeZone).endOf('day').toDate();
    const last3Months = Array.from({ length: 3 }).map((_, i) =>
      parseInt(moment.tz(timeZone).subtract(i, 'months').format('YYYYMM')),
    );

    const userDeviceRecordData = await this.userDeviceRecord.find({
      userId,
      isDeleted: false,
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    const userGoalData = await this.userGoal.find({
      userId,
      createdAt: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    const userMoodRecordData = await this.userMoodRecords.find({
      userId,
      createdAt: {
        $gte: startDate,
        $lte: endDate,
      },
    });
    const userMealRecordData = await this.userMealRecords.find({
      userId,
      isDeleted: false,
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    const monthlyWeightSummaryData = await this.monthlyWeightSummary.find({
      userId,
      month: { $in: last3Months },
    });

    const userSleepRecords = await this.userSleepRecords.find({
      userId,
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    const filteredData = {
      userDeviceRecordData,
      userGoalData,
      userMoodRecordData,
      userMealRecordData,
      monthlyWeightSummaryData,
      userSleepRecords,
    };

    const encrypted = this.dataEncryptionService.encrypt(
      JSON.stringify(filteredData),
    );
    return encrypted;
    // return {filteredData };
  }
}
