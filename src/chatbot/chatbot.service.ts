import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as moment from 'moment-timezone';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MESSAGE_ROLE, Chat_Message, Chat_Session } from 'src/models/chat';
import { Logger } from '@nestjs/common';
import {
  AskChatBotResDTO,
  ChatBotReqDTO,
  ChatBotResponseDTO,
  ChatMessageDTO,
  GetAllSessionMessagesResDTO,
} from './dto';
import { Types } from 'mongoose';
import { GetAllSessionMessagesQueryInterface } from './interfaces';
import { HealthDataService } from './health-data/health-data.service';
import {
  SUPPORTED_CATEGORIES,
  CLASSIFICATION_PROMPT,
  ChatbotCategory,
  CHATBOT_PROMPT_TEMPLATE,
  getAttachmentPrompt,
} from 'src/chatbot/prompts';
import { ChatbotUtilsService } from './chatbot.utils.service';
import { UtilsService } from 'src/common/services/utils.service';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { HealthDeviceService } from './health-device/health-device.service';
import { BuildMessagePromptService } from 'src/chatbot/prompts';
import { ChatbotRedisService } from 'src/chatbot/cache-service/chatbot.redis.service';
import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ChatbotService {
  private readonly logger = new Logger(ChatbotService.name);
  private stripMarkdownBold(text: string): string {
    return text.replace(/\*\*(.*?)\*\*/g, '$1');
  }

  constructor(
    @InjectModel(Chat_Message.name) private messageModel: Model<Chat_Message>,
    @InjectModel(Chat_Session.name) private sessionModel: Model<Chat_Session>,

    private readonly healthDataService: HealthDataService,
    private readonly buildMessagePromptService: BuildMessagePromptService,
    private readonly healthDeviceService: HealthDeviceService,
    private readonly chatbotUtilsService: ChatbotUtilsService,
    private readonly chatbotRedisService: ChatbotRedisService,
    private readonly configService: ConfigService,
    private readonly utilsService: UtilsService,
  ) {}

  async askChatBot(
    reqBody: ChatBotReqDTO,
    token: string,
  ): Promise<AskChatBotResDTO> {
    try {
      const { sessionId, user, attachment } = reqBody;
      console.log('reqBody', reqBody.attachment);
      const originalQuestion = reqBody.question;
      const expiryDurationMs = parseInt(
        this.configService.get<string>('CHATBOT_SESSION_EXPIRY') || '86400000',
        10,
      );
      //   console.log('expiryDurationMs', expiryDurationMs);
      if (!user?.timeZone || !moment.tz.zone(user.timeZone)) {
        this.logger.warn(
          `Invalid or missing timeZone for user: ${user._id}, using UTC fallback`,
        );
      }
      //   console.log('userId', user._id);
      //   console.log('', user.timeZone);
      const userTimeZone = user?.timeZone?.trim();
      //   console.log('userTimeZone', userTimeZone);
      const question =
        this.chatbotUtilsService.resolveRelativeDates(originalQuestion);

      let existing_chat_session: Chat_Session | null = null;

      const [healthApiRes, fallbackHealthDataRes] = await Promise.all([
        this.healthDataService.findUserhealthData(user),
        this.healthDataService.fetchEncryptedHealthData(user._id),
      ]);

      const decryptedHealthRes = await this.healthDeviceService.getHealthData(
        user._id,
      );
      const usermenuData = decryptedHealthRes?.data ?? null;

      const healthMicroserviceData = healthApiRes?.data ?? null;
      const healthDump = fallbackHealthDataRes?.data ?? null;
      const mergedHealthContext = {
        microservice: healthMicroserviceData,
        dump: healthDump,
      };

      if (sessionId && Types.ObjectId.isValid(sessionId)) {
        const document = await this.sessionModel.findById(sessionId);
        if (!document) {
          throw new NotFoundException('session not found');
        }
        if (document.userId.toString() !== user._id.toString()) {
          throw new ForbiddenException(
            'Access denied: User does not own this document',
          );
        }

        if (moment().isAfter(moment(document.expiredAt))) {
          await this.sessionModel.updateOne(
            { _id: document._id },
            { $set: { isExpired: true } },
          );
          existing_chat_session =
            await this.chatbotUtilsService.createNewSession(user);
        } else {
          existing_chat_session = document;
        }
      } else {
        const lastActive = await this.sessionModel
          .findOne({
            userId: user._id,
            isExpired: false,
          })
          .sort({ createdAt: -1 });

        if (lastActive && moment().isAfter(moment(lastActive.expiredAt))) {
          await this.sessionModel.updateOne(
            { _id: lastActive._id },
            { $set: { isExpired: true } },
          );
          existing_chat_session =
            await this.chatbotUtilsService.createNewSession(user);
        } else if (lastActive) {
          await this.sessionModel.updateOne(
            { _id: lastActive._id },
            { $set: { isExpired: true } },
          );
          existing_chat_session =
            await this.chatbotUtilsService.createNewSession(user);
        } else {
          existing_chat_session =
            await this.chatbotUtilsService.createNewSession(user);
        }

        await this.chatbotRedisService.clearAllUserSessionCaches(user._id);
      }

      let keyIndex = 0;
      try {
        const tempPrompt = [
          new SystemMessage('Pre-flight check'),
          new HumanMessage('ping'),
        ];
        const res = await this.chatbotUtilsService.askWithFallback(tempPrompt);
        keyIndex = res.keyIndex;
      } catch (err) {
        console.log(err);
        throw new InternalServerErrorException('OpenAI fallback failed');
      }
      const category = await this.classifyPromptUsingLLM(
        question,
        existing_chat_session,
        keyIndex,
      );

      console.log('category', category);

      const selectedPromptTemplate = CHATBOT_PROMPT_TEMPLATE[category];

      const attachmentPrompt = getAttachmentPrompt({
        attachment: reqBody.attachment,
      });

      const adminContactInfo = this.chatbotUtilsService.getAdminContactInfo();

      const allMessages = await this.messageModel
        .find({ sessionId: existing_chat_session._id })
        .sort({ createdAt: 1 })
        .lean();

      const fullMessageTimeline = allMessages.map(ChatMessageDTO.transform);

      const chatHistory = fullMessageTimeline.map((msg) => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp,
      }));

      //   console.log('chatHistory', chatHistory);

      const prompt = this.chatbotUtilsService.buildPrompt(
        selectedPromptTemplate,
        user,
        mergedHealthContext,
        question,
        usermenuData,
        attachmentPrompt,
        adminContactInfo,
      );

      const systemPrompt =
        this.buildMessagePromptService.buildStrictSystemMessage();
      const llmMessages = this.chatbotUtilsService.buildLLMMessages(
        prompt,
        systemPrompt,
        chatHistory,
      );

      if (reqBody.attachment?.type === 'image' && reqBody.attachment.url) {
        const imageUrl = reqBody.attachment.url;

        const lastMessage = llmMessages.find((m) => m.role === 'user');
        if (lastMessage && typeof lastMessage.content === 'string') {
          (lastMessage as any).content = [
            {
              type: 'text',
              text: lastMessage.content,
            },
            {
              type: 'image_url',
              image_url: { url: imageUrl },
            },
          ];
        }
      } else if (
        reqBody.attachment?.type === 'application' &&
        reqBody.attachment.url
      ) {
        const documentUrl = reqBody.attachment.url;
        const safeUserId = user._id.replace(/[^a-zA-Z0-9-_]/g, '');
        const tempFilePath = path.join(
          '/tmp',
          `attachment-${safeUserId}-${Date.now()}.pdf`,
        );

        // try {
        //   // Download PDF using fetch
        //   const response = await fetch(documentUrl);
        //   if (!response.ok) {
        //     throw new Error(
        //       `Failed to fetch PDF: ${response.status} ${response.statusText}`,
        //     );
        //   }
        //   const buffer = Buffer.from(await response.arrayBuffer());
        //   await fs.writeFile(tempFilePath, buffer);
        //   const loader = new PDFLoader(tempFilePath);
        //   const docs = await loader.load();
        //   const lastMessage = llmMessages.find((m) => m.role === 'user');
        //   if (lastMessage && typeof lastMessage.content === 'string') {
        //     (lastMessage as any).content = [
        //       {
        //         type: 'text',
        //         text: lastMessage.content,
        //       },
        //       {
        //         type: 'text',
        //         text: docs.map((d) => d.pageContent).join('\n\n'),
        //       },
        //     ];
        //   }
        //   await fs.unlink(tempFilePath);
        // } catch (err) {
        //   try {
        //     await fs.unlink(tempFilePath);
        //   } catch (cleanupErr) {
        //     this.logger.warn(
        //       `Failed to delete temp PDF file at ${tempFilePath}: ${cleanupErr.message}`,
        //     );
        //   }
        //   throw new BadRequestException(
        //     `Failed to process attached PDF: ${err.message}`,
        //   );
        // }
      }

      let response;
      try {
        const res = await this.chatbotUtilsService.askWithKeyIndex(
          llmMessages,
          keyIndex,
        );
        response = { content: res.content };
      } catch (error) {
        console.error('OpenAI API Error:', error);
        throw new InternalServerErrorException(
          'Failed to get response please try again later',
        );
      }

      const userMessage = await this.messageModel.create({
        role: MESSAGE_ROLE.USER,
        content: question,
        fileId: attachment?.fileId || null,
        fileName: attachment?.name || null,
        fileType: attachment?.mimetype || null,
        sessionId: existing_chat_session?._id,
      });
      await this.messageModel.create({
        role: MESSAGE_ROLE.ASSISTANT,
        content: response.content,
        sessionId: existing_chat_session?._id,
        questionId: userMessage._id,
      });

      const { reply: extractedReply, link } =
        this.chatbotUtilsService.extractLinkFromReply(
          this.stripMarkdownBold(response.content),
        );
      const resp = ChatBotResponseDTO.transform({
        sessionId: existing_chat_session?._id,
        reply: extractedReply,
        link,
        fileId: attachment?.fileId || null,
        fileName: attachment?.name || null,
        fileType: attachment?.mimetype || null,
        expiredAt: existing_chat_session.expiredAt.toISOString(),
      });

      const updatedMessages = await this.messageModel
        .find({ sessionId: existing_chat_session._id })
        .sort({ createdAt: 1 })
        .lean();

      const updatedTimeline = updatedMessages.map(ChatMessageDTO.transform);

      await this.chatbotRedisService.setCachedSessionMessages(
        user._id,
        existing_chat_session._id?.toString() || '',
        updatedTimeline,
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: resp,
        createdAt: userMessage.createdAt.toISOString(),
      };
    } catch (error) {
      throw error;
    }
  }
  async getAllSessionMessages(
    sessionId: string,
    queryFilters: GetAllSessionMessagesQueryInterface,
  ): Promise<GetAllSessionMessagesResDTO> {
    if (!Types.ObjectId.isValid(sessionId)) {
      throw new BadRequestException('Invalid Session Id Provided !!');
    }

    const { userId } = queryFilters;

    if (!userId) {
      throw new InternalServerErrorException('Please provide user Id !!');
    }

    const existing_chat_session = await this.sessionModel.findOne({
      _id: sessionId,
      isExpired: false,
      userId,
    });

    console.log('existing_chat_session', existing_chat_session);

    // console.log('existing_chat_session', existing_chat_session);
    if (!existing_chat_session) {
      throw new BadRequestException('Session Not Found !!');
    }

    const now = new Date();
    const isSessionExpired =
      existing_chat_session.isExpired ||
      (existing_chat_session.expiredAt &&
        existing_chat_session.expiredAt.getTime() <= now.getTime());

    if (isSessionExpired) {
      if (!existing_chat_session.isExpired) {
        await this.sessionModel.updateOne(
          { _id: existing_chat_session._id },
          { $set: { isExpired: true } },
        );
      }

      await this.chatbotRedisService.clearAllUserSessionCaches(userId);
    } else {
      const cachedMessages =
        await this.chatbotRedisService.getCachedSessionMessages(
          userId,
          sessionId,
        );
      if (cachedMessages) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          data: {
            sessionId,
            messages: cachedMessages,
          },
        };
      }
    }

    // Fallback: fetch from MongoDB
    const messages = await this.messageModel
      .find({ sessionId: existing_chat_session._id })
      .sort({ createdAt: 1 })
      .lean();
    if (!messages.length) {
      return {
        error: true,
        statusCode: HttpStatus.NOT_FOUND,
        data: null,
        message: 'Session expired, no records found',
      };
    }
    // console.log('messages', messages);
    const formattedMessages = messages.map(ChatMessageDTO.transform);
    // console.log('formattedMessages', formattedMessages);
    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: {
        sessionId: existing_chat_session._id?.toString() || '',
        messages: formattedMessages,
      },
    };
  }

  async classifyPromptUsingLLM(
    question: string,
    session: Chat_Session,
    keyIndex: number,
  ): Promise<ChatbotCategory> {
    try {
      const prompt = CLASSIFICATION_PROMPT.replace('{question}', question);

      const systemPrompt = `You are a helpful assistant that only responds with one of the allowed category labels.`;
      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(prompt),
      ];

      const res = await this.chatbotUtilsService.askWithKeyIndex(
        messages,
        keyIndex,
      );

      const rawCategory = res.content?.toString().trim().toLowerCase();
      const matched = SUPPORTED_CATEGORIES.find(
        (cat) => cat.toLowerCase() === rawCategory,
      );

      if (!matched) {
        this.logger.warn('Unexpected category:', rawCategory);
        return 'activity';
      }

      return matched;
    } catch (error) {
      this.logger.error(
        `LLM Classification Error: ${(error as Error).message}`,
      );
      return 'activity';
    }
  }
}
