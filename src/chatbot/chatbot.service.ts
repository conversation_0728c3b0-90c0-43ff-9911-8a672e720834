import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import {
  AskChatbotQueryReqDTO,
  AskChatbotQueryResDTO,
  GetAllSessionMessagesResDTO,
} from './dtos';
import { User } from 'models/user';
import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import { GetAllSessionMessagesQueryInterface } from './interfaces';
import { Types } from 'mongoose';
import { RedisCacheService } from 'src/redis/redis.service';
import { CustomLogger } from 'src/common/services';
import { FileUploadService } from 'src/user/file-upload/file-upload.service';

export enum CHATBOT_ATTACHMENTS_TYPES {
  IMAGE = 'IMAGE',
  APPLICATION = 'APPLICATION',
}

@Injectable()
export class ChatbotService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
    private readonly redisCacheService: RedisCacheService,
    private readonly logger: CustomLogger,
    private readonly fileUploadService: FileUploadService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('CHATBOT_MICROSERVICE_URL')}/chat`;

    this.apiKey = this.configService.get<string>(
      'CHATBOT_MICROSERVICE_API_KEY',
    );

    this.logger.log(`Chatbot Service Initialized`);
    this.logger.log(`Microservice URL: ${this.MICROSERVICE_URL}`);
  }

  async askChatBotQuery(
    user: User,
    queryBody: AskChatbotQueryReqDTO,
    attachment?: Express.Multer.File,
  ): Promise<AskChatbotQueryResDTO> {
    const { question, sessionId } = queryBody;
    const userId = user._id.toString();

    const userObj = {
      _id: userId,
      age: 50,
      gender: user.gender,
      height: user.height,
      weight: user.weight,
      timeZone: user.timeZone,
      goals: user.goals.map((item) => {
        return { goal_type: item.goal_type, selected_goal: item.selected_goal };
      }),
    };

    let uploadedFileObj = null;
    if (attachment) {
      const type = attachment.mimetype.split('/')[0];
      if (type.toUpperCase() in CHATBOT_ATTACHMENTS_TYPES) {
        const uploadedFile =
          await this.fileUploadService.uploadAndStoreFile(attachment);
        uploadedFileObj = {
          fileId: uploadedFile._id.toString(),
          type,
          url: await this.fileUploadService.getValidS3Url(
            uploadedFile._id.toString(),
          ),
          size: uploadedFile.size,
          name: uploadedFile.fileName,
          mimetype: uploadedFile.fileType,
        };
      } else {
        throw new BadRequestException(`${type} files are not supported.`);
      }
    }

    // 1. Check cache for existing session messages

    let cachedMessages = [];
    let isNewSession = false;

    if (sessionId) {
      const cachedSession = await this.redisCacheService.get<any>(
        `chat_session:${sessionId}`,
      );
      cachedMessages = cachedSession?.messages || [];
    } else {
      isNewSession = true;
    }

    const data = await this.microserviceClient.post(
      this.apiKey,
      this.MICROSERVICE_URL,
      {
        user: userObj,
        question,
        sessionId: sessionId || null,
        attachment: uploadedFileObj,
      },
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }
    if (data?.resp?.data) {
      const responseData = data.resp.data;
      const newSessionId = responseData.sessionId || sessionId;

      if (newSessionId) {
        if (isNewSession && responseData.sessionId) {
          await this.redisCacheService.deletePattern(
            `chat_session:*:user:${userId}`,
          );
        }

        const updatedMessages = [
          ...cachedMessages,
          {
            role: 'user',
            content: question,
            timestamp: new Date().toISOString(),
          },
          {
            role: 'assistant',
            content: responseData.reply,
            timestamp: new Date().toISOString(),
          },
        ].slice(-20);

        const cacheKey = `chat_session:${newSessionId}:user:${userId}`;

        await this.redisCacheService.set(
          cacheKey,
          {
            sessionId: newSessionId,
            userId,
            messages: updatedMessages,
            lastUpdated: new Date().toISOString(),
          },
          3600, // 1 hour TTL
        );
      }
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
      createdAt: data?.resp?.createdAt,
    };
  }

  async getAllSessionMessages(
    sessionId: string,
    queryFilters: GetAllSessionMessagesQueryInterface,
    user: User,
  ): Promise<GetAllSessionMessagesResDTO> {
    if (!Types.ObjectId.isValid(sessionId)) {
      throw new BadRequestException('Invalid Session Id provided !!');
    }

    const { page } = queryFilters;

    const data = await this.microserviceClient.get(
      this.apiKey,
      `${this.MICROSERVICE_URL}/${sessionId}?page=${page || 1}&userId=${user._id.toString()}`,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
      page: Number(page) || 1,
    };
  }
}
