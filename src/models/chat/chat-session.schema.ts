import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Chat_Session extends Document<Types.ObjectId> {
  @Prop({ type: Types.ObjectId, required: true })
  userId: Types.ObjectId;

  @Prop({ type: String, required: false, default: null })
  healthDataId?: string;

  @Prop({ type: Boolean, default: false })
  isExpired: boolean;

  //set expired at
  @Prop({ type: Date, default: null })
  expiredAt: Date;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const Chat_SessionSchema = SchemaFactory.createForClass(Chat_Session);

Chat_SessionSchema.index({ userId: 1, isExpired: 1, createdAt: -1 });
