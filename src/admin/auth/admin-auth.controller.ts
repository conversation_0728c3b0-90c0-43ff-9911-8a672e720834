import { Controller, Get, Post, Body, Req, UseGuards } from '@nestjs/common';
import { AdminAuthService } from './admin-auth.service';
import { AuthGuard } from 'src/middlewares';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { Authority } from 'src/utils/decorators';
import {
  LoginReqDto,
  LoginResDto,
} from 'src/auth/credentials-auth/credentials-auth-dto';

@ApiTags('Admin-Auth')
@ApiBearerAuth()
@Controller('/admin')
export class AdminAuthController {
  constructor(private readonly adminAuthService: AdminAuthService) {}
  @ApiOperation({
    summary: 'Admin Login',
    description: 'Login as admin using email and password',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully logged in',
    type: LoginResDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Email not verified or invalid credentials',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - not an admin',
    type: ErrorResponse,
  })
  @Post('login')
  async adminLogin(@Body() loginData: LoginReqDto): Promise<LoginResDto> {
    return this.adminAuthService.login(loginData, true);
  }

  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @UseGuards(AuthGuard)
  @Get('logout')
  @ApiOperation({
    summary: 'Admin logout',
    description: 'Logs out the currently authenticated admin.',
  })
  @ApiResponse({ status: 200, description: 'Successfully logged out' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Authority()
  async logout(@Req() req): Promise<{ message: string }> {
    const accessToken = req.headers['authorization']?.split(' ')[1];
    await this.adminAuthService.adminLogout(accessToken);
    return { message: 'Logged out successfully' };
  }
}
