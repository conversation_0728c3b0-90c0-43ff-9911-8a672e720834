import {
  UnauthorizedException,
  BadRequestException,
  HttpStatus,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AccessToken, RefreshToken } from 'models/auth';
import { Model } from 'mongoose';
import {
  LoginReqDto,
  LoginResDto,
} from 'src/auth/credentials-auth/credentials-auth-dto';
import { CredentialsAuthUtilsService } from 'src/auth/credentials-auth/credentials-auth-utils.service';

@Injectable()
export class AdminAuthService {
  constructor(
    @InjectModel(AccessToken.name)
    private readonly accessTokenModel: Model<AccessToken>,

    @InjectModel(RefreshToken.name)
    private readonly refreshTokenModel: Model<RefreshToken>,
    private readonly credentialsAuthUtilsService: CredentialsAuthUtilsService,
  ) {}

  async adminLogout(authHeader: string): Promise<any> {
    if (!authHeader) {
      throw new UnauthorizedException(
        'Access token is missing. Please log in again.',
      );
    }

    if (!authHeader) {
      throw new UnauthorizedException(
        'Access token is missing. Please log in again.',
      );
    }

    const accessToken = await this.accessTokenModel.findOne({
      token: authHeader,
    });
    if (!accessToken) {
      throw new UnauthorizedException(
        'Invalid access token. Please log in again.',
      );
    }

    const { refreshTokenId } = accessToken;
    const result = await this.refreshTokenModel.updateOne(
      { _id: refreshTokenId },
      { expiry: Date.now(), isExpired: true },
    );

    if (result.modifiedCount === 0) {
      throw new BadRequestException('Failed to invalidate refresh token.');
    }

    await this.accessTokenModel.updateOne(
      { token: authHeader },
      { expiry: Date.now(), isExpired: true },
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'You have logged out successfully!',
    };
  }

  async login(
    loginData: LoginReqDto,
    isAdminRoute = false,
  ): Promise<LoginResDto> {
    const { email, password } = loginData;

    const user = await this.credentialsAuthUtilsService.validateUser(email);

    if (!user.isEmailVerified) {
      throw new ForbiddenException(
        'Please verify your account first before login.',
      );
    }

    if (isAdminRoute && user.role !== 'admin') {
      throw new UnauthorizedException(
        'You are not authorized to access this route.',
      );
    }

    await this.credentialsAuthUtilsService.verifyAccountPassword(
      user,
      password,
    );

    const refreshTokenData =
      await this.credentialsAuthUtilsService.generateRefreshToken(user);
    const accessTokenData =
      await this.credentialsAuthUtilsService.generateAccessToken(
        user,
        refreshTokenData,
      );

    return this.credentialsAuthUtilsService.sendLoginResponse(
      user,
      accessTokenData.token,
      accessTokenData.expiryInMs,
    );
  }
}
