import { Controller, Post, Body, Req } from '@nestjs/common';
import { AdminRegisterService } from './admin-register.service';
import { CreateAdminRegisterDto } from './dto/create-admin-register.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';

@ApiTags('Admin-Register')
@ApiBearerAuth()
@Controller('admin-register')
export class AdminRegisterController {
  constructor(private readonly adminRegisterService: AdminRegisterService) {}

  @Post()
  create(
    @Body() createAdminRegisterDto: CreateAdminRegisterDto,
    @Req() req: Request,
  ) {
    return this.adminRegisterService.create(createAdminRegisterDto, req);
  }
}
