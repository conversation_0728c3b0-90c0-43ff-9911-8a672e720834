import { Modu<PERSON> } from '@nestjs/common';
import { AdminPermissionController } from './admin-permissions/admin-permission.controller';
import { AdminPermissionService } from './admin-permissions/admin-permission.service';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { AdminUsersController } from './admin-users/admin-users.controller';
import { AdminUsersService } from './admin-users/admin-users.service';
import { AdminActivityController } from './admin-activity-videos/admin-activity.controller';
import { AdminActivityService } from './admin-activity-videos/admin-activity.service';
import { AdminMoodController } from './admin-mood-videos/admin-mood.controller';
import { AdminMoodService } from './admin-mood-videos/admin-mood.service';
import { MongooseModule } from '@nestjs/mongoose';
import { MoodMedia, MoodMediaSchema } from 'models/mood';
import { Activity, ActivitySchema } from 'models/activity';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { RepoModule } from 'src/repo/repo.module';
import { AdminContactUsQueryController } from './admin-contact-us-query/admin-contact-us-query.controller';
import { AdminContactUsQueryService } from './admin-contact-us-query/admin-contact-us-query.service';
import { ContactUsQuery, ContactUsQuerySchema } from 'models/contact-us-query';
import { AdminRecipeController } from './admin-recipe/admin-recipe.controller';
import { AdminRecipeService } from './admin-recipe/admin-recipe.service';
import { Recipes, RecipesSchema } from 'models/recipe';
import { AdminCreateRecipeUtilsService } from './admin-recipe/admin-create-recipe-utils.service';
import { AdminDeviceController } from './admin-device-management/admin-device.controller';
import { AdminDeviceService } from './admin-device-management/admin-device.service';
import { AdminCreateDeviceUtilsService } from './admin-device-management/admin-add-device-utils.service';
import { Device, DeviceSchema } from 'models/device';
import { AdminHelpService } from './admin-help/admin-help.service';
import { AdminHelpController } from './admin-help/admin-help.controller';
import { Help, HelpSchema } from 'models/help';
import { AdminFaqController } from './admin-faq/admin-faq.controller';
import { AdminFaqService } from './admin-faq/admin-faq.service';
import { Faq, FaqSchema } from 'models/faq';
import {
  UserMealRecords,
  UserMealRecordsSchema,
} from 'models/user-records/Meal-Records';

import {
  UserJourneyPerDay,
  UserJourneyPerDaySchema,
} from 'models/user-journey';

import { IngredientController } from './admin-ingredient/admin-ingredient.controller';
import { IngredientService } from './admin-ingredient/admin-ingredient.service';
import { Ingredient } from 'models/ingredient';
import { IngredientSchema } from 'models/ingredient/ingredient.schema';
import { AdminIngredientUtilsService } from './admin-ingredient/admin-ingredient-utils.service';

import { AdminAuthController } from './auth/admin-auth.controller';
import { AdminAuthService } from './auth/admin-auth.service';
import { JwtModule } from '@nestjs/jwt';
import {
  UserDeviceConnections,
  UserDeviceConnectionsSchema,
} from 'models/device';

import { RouteAccessToken, RouteAccessTokenSchema } from 'models/auth';

import {
  UserDeviceRecord,
  UserDeviceRecordSchema,
} from 'models/user-records/Device-records';

import { AdminAnalyticsModule } from './admin-analytics/admin-analytics.module';

import { WeightAnalyticModule } from './admin-analytics/weight/weight-analytics.module';
import { DeviceAnalyticsModule } from './admin-analytics/device/device-analytics.module';
import { AdminOtpModule } from './admin-otp-verification/admin-otp.module';
import { AdminRegisterModule } from './admin-register/admin-register.module';
import { CredentialsAuthUtilsService } from 'src/auth/credentials-auth/credentials-auth-utils.service';
import { FileUploadModule } from 'src/user/file-upload/file-upload.module';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '15m' },
    }),
    MongooseModule.forFeature([
      { name: MoodMedia.name, schema: MoodMediaSchema },
      { name: Activity.name, schema: ActivitySchema },
      { name: ContactUsQuery.name, schema: ContactUsQuerySchema },
      { name: Recipes.name, schema: RecipesSchema },
      { name: Device.name, schema: DeviceSchema },
      { name: Help.name, schema: HelpSchema },
      { name: Faq.name, schema: FaqSchema },
      { name: UserMealRecords.name, schema: UserMealRecordsSchema },
      { name: UserJourneyPerDay.name, schema: UserJourneyPerDaySchema },
      { name: UserDeviceConnections.name, schema: UserDeviceConnectionsSchema },
      { name: Ingredient.name, schema: IngredientSchema },
      { name: UserDeviceConnections.name, schema: UserDeviceConnectionsSchema },
      { name: UserDeviceRecord.name, schema: UserDeviceRecordSchema },
      { name: RouteAccessToken.name, schema: RouteAccessTokenSchema },
    ]),
    AuthModule,
    CommonModule,
    ThirdPartyModule,
    RepoModule,
    AdminAnalyticsModule,
    WeightAnalyticModule,
    DeviceAnalyticsModule,
    AdminOtpModule,
    AdminRegisterModule,
    FileUploadModule,
  ],
  controllers: [
    AdminPermissionController,
    AdminUsersController,
    AdminActivityController,
    AdminMoodController,
    AdminContactUsQueryController,
    AdminRecipeController,
    AdminDeviceController,
    AdminHelpController,
    AdminFaqController,
    AdminAuthController,
    IngredientController,
    AdminAuthController,
  ],
  providers: [
    AdminPermissionService,
    AdminUsersService,
    AdminActivityService,
    AdminMoodService,
    AdminContactUsQueryService,
    AdminRecipeService,
    AdminCreateRecipeUtilsService,
    AdminDeviceService,
    AdminCreateDeviceUtilsService,
    AdminHelpService,
    AdminFaqService,
    AdminAuthService,
    IngredientService,
    AdminIngredientUtilsService,
    AdminAuthService,
    CredentialsAuthUtilsService,
  ],
})
export class AdminModule {}
