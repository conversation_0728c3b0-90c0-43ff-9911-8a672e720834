import {
  Controller,
  Get,
  Query,
  Param,
  Delete,
  UseGuards,
  UnauthorizedException,
  HttpStatus,
  Catch,
  ExceptionFilter,
  ArgumentsHost,
  UseFilters,
} from '@nestjs/common';
import { AdminUsersService } from './admin-users.service';
import { getAllUsersQueryInterface } from './interface';
import { Authority } from 'src/utils/decorators';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { GetAllUsersResDTO } from './dto';
import { GetSingleUsersResDTO } from './dto';
import { DeleteUserResDTO } from './dto';
import { OtpAccessGuard } from 'src/middlewares/otpAccessGaurd.middleware';

@Catch(UnauthorizedException)
export class OtpExceptionFilter implements ExceptionFilter {
  catch(exception: UnauthorizedException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();

    response.status(HttpStatus.UNAUTHORIZED).json({
      error: true,
      statusCode: HttpStatus.UNAUTHORIZED,
      message: exception.message,
      isInvalidOTP: true,
      errorId: new Date().getTime(),
      timestamp: new Date(),
    });
  }
}

@ApiTags('Admin-Users')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin/users')
@UseFilters(OtpExceptionFilter)
export class AdminUsersController {
  constructor(private readonly adminUserService: AdminUsersService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user profiles.',
    type: GetAllUsersResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or expired OTP',
    schema: {
      properties: {
        error: { type: 'boolean', example: true },
        statusCode: { type: 'number', example: 401 },
        message: {
          type: 'string',
          example: 'Invalid or expired OTP access token',
        },
        isInvalidOTP: { type: 'boolean', example: true },
        errorId: { type: 'number', example: 1605516011758 },
        timestamp: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiQuery({
    name: 'page',
    description: 'The page number for pagination (optional)',
    required: false,
    type: String,
    example: '1', // Example for Swagger documentation
  })
  @Authority()
  @UseGuards(OtpAccessGuard)
  @Get()
  async getAllUsers(@Query() filterQueries: getAllUsersQueryInterface) {
    return this.adminUserService.getAllUsers(filterQueries);
  }

  // Get a single user by ID
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user profile.',
    type: GetSingleUsersResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or expired OTP',
    schema: {
      properties: {
        error: { type: 'boolean', example: true },
        statusCode: { type: 'number', example: 401 },
        message: {
          type: 'string',
          example: 'Invalid or expired OTP access token',
        },
        isInvalidOTP: { type: 'boolean', example: true },
        errorId: { type: 'number', example: 1605516011758 },
        timestamp: { type: 'string', format: 'date-time' },
      },
    },
  })
  @Authority()
  @UseGuards(OtpAccessGuard)
  @Get(':userId')
  async getSingleUser(@Param('userId') id: string) {
    return this.adminUserService.getSingleUser(id);
  }

  // Delete a user by ID
  @ApiResponse({
    status: 200,
    description: 'User successfully deleted.',
    type: DeleteUserResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or expired OTP',
    schema: {
      properties: {
        error: { type: 'boolean', example: true },
        statusCode: { type: 'number', example: 401 },
        message: {
          type: 'string',
          example: 'Invalid or expired OTP access token',
        },
        isInvalidOTP: { type: 'boolean', example: true },
        errorId: { type: 'number', example: 1605516011758 },
        timestamp: { type: 'string', format: 'date-time' },
      },
    },
  })
  @Authority()
  @UseGuards(OtpAccessGuard)
  @Delete(':userId')
  async deleteUser(@Param('userId') id: string) {
    return this.adminUserService.deleteUser(id);
  }
}
