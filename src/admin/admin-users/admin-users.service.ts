import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Model } from 'mongoose';
import { getAllUsersQueryInterface } from './interface';
import { EncryptionService, UtilsService } from 'src/common/services';
import { ROLE_VALUES } from 'models/user/user.schema';
import {
  GetAllUsersResDTO,
  GetSingleUsersResDTO,
  DeleteUserResDTO,
  AdminUserDTO,
} from './dto';

@Injectable()
export class AdminUsersService {
  constructor(
    @InjectModel('User') private readonly userModel: Model<User>,
    private readonly utilsService: UtilsService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async getAllUsers(
    queryFilters: getAllUsersQueryInterface,
  ): Promise<GetAllUsersResDTO> {
    const { page, isDeleted, email } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { role: { $ne: ROLE_VALUES.ADMIN } };

    if (isDeleted !== '') {
      query.isDeleted = isDeleted === 'true' ? true : false;
    }

    if (email) {
      try {
        const encryptedSearchEmail = this.encryptionService.encrypt(email);

        query.email = encryptedSearchEmail;
      } catch (error) {
        console.error('Decryption failed:', error);
        throw new Error('Invalid email format or decryption error.');
      }
    }

    const users = await this.userModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.userModel.countDocuments({
      role: { $ne: ROLE_VALUES.ADMIN },
    });

    const userResp = users.map((item) => {
      item.email = this.encryptionService.decrypt(item.email);
      return AdminUserDTO.transform(item);
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: userResp.length,
      users: userResp,
    };
  }

  async getSingleUser(userId: string): Promise<GetSingleUsersResDTO> {
    const user = await this.userModel.findById(userId).exec();

    if (!user) {
      throw new BadRequestException('User not found');
    }

    user.email = this.encryptionService.decrypt(user.email);
    const userResp = AdminUserDTO.transform(user);

    return { error: false, statusCode: HttpStatus.OK, user: userResp };
  }

  async deleteUser(userId: string): Promise<DeleteUserResDTO> {
    const user = await this.userModel
      .findByIdAndUpdate(userId, { isDeleted: true })
      .exec();

    if (!user) {
      throw new BadRequestException('User not found or already deleted');
    }

    const userResp = AdminUserDTO.transform(user);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'User deleted successfully',
      user: userResp,
    };
  }
}
