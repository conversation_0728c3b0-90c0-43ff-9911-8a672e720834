import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { AdminUserDTO } from './adminUser.dto';

export class DeleteUserResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message indicating the result of the deletion action',
    example: 'User deleted successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'The user profile of the deleted user',
    type: AdminUserDTO,
  })
  user: AdminUserDTO;
}
