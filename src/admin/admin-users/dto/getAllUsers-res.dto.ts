import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { AdminUserDTO } from './adminUser.dto';

export class GetAllUsersResDTO extends BaseResponse {
  @ApiProperty({
    example: 100,
    description: 'Total number of users (excluding admins)',
  })
  total: number;

  @ApiProperty({
    example: 10,
    description: 'Number of users returned in the response',
  })
  nbHits: number;

  @ApiProperty({
    type: [AdminUserDTO],
    description: 'Array of user profiles',
  })
  users: AdminUserDTO[];
}
