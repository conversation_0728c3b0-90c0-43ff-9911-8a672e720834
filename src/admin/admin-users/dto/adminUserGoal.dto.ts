import { ApiProperty } from '@nestjs/swagger';
import { GOAL_TYPES, UserGoal } from 'models/user';

export class AdminUserGoalDTO {
  @ApiProperty({ description: 'Unique identifier of the goal' })
  id: string;

  @ApiProperty({
    description: 'Type of the goal',
    enum: GOAL_TYPES,
  })
  goal_type: GOAL_TYPES;

  @ApiProperty({ description: 'Selected goal value' })
  selected_goal: string;

  static transform(object: UserGoal): AdminUserGoalDTO {
    const transformedObj: AdminUserGoalDTO = new AdminUserGoalDTO();

    transformedObj.id = object.id;
    transformedObj.goal_type = object.goal_type;
    transformedObj.selected_goal = object.selected_goal;

    return transformedObj;
  }
}
