import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { GENDER_TYPES } from 'models/user';
import { DIET_PREFERENCE, User } from 'models/user/user.schema';
import { AdminUserGoalDTO } from './adminUserGoal.dto';

export class AdminUserDTO {
  @ApiProperty({
    example: '123',
  })
  id: string;

  @ApiProperty({
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isEmailVerified: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isDeleted: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isAccountCompleted: boolean;

  @ApiProperty({
    example: 22,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  age?: number;

  @ApiProperty({
    example: 'male',
    required: false,
  })
  @IsOptional()
  @IsEnum(GENDER_TYPES)
  gender?: GENDER_TYPES;

  @ApiProperty({
    example: 170,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    example: 70,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  weight?: number;

  @ApiProperty({
    example: 'veg',
    required: false,
  })
  @IsOptional()
  @IsEnum(DIET_PREFERENCE)
  diet_preference?: DIET_PREFERENCE;

  @ApiProperty({
    example: [],
    required: false,
  })
  goals?: AdminUserGoalDTO[];

  @ApiProperty({
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  deviceUsageLimit: number;

  static transform(object: User): AdminUserDTO {
    const transformedObj: AdminUserDTO = new AdminUserDTO();

    transformedObj.id = object._id.toString();
    transformedObj.email = object.email;
    transformedObj.name = object.name;
    transformedObj.isEmailVerified = object.isEmailVerified;
    transformedObj.isAccountCompleted = object.isAccountCompleted;
    transformedObj.isDeleted = object.isDeleted;

    transformedObj.age = object.age;
    transformedObj.gender = object.gender;
    transformedObj.height = object.height;
    transformedObj.weight = object.weight;
    transformedObj.deviceUsageLimit = object.deviceUsageLimit;
    transformedObj.diet_preference = object.diet_preference;

    transformedObj.goals = object.goals.map((item) =>
      AdminUserGoalDTO.transform(item),
    );

    return transformedObj;
  }
}
