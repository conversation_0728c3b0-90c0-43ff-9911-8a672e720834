import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  NotificationMessage,
  NotificationMessageSchema,
  NotificationToken,
  NotificationTokenSchema,
} from 'models/notificationToken';
import { ReminderSettings, RemindersSettingsSchema } from 'models/reminders';
import { ActiveMQService } from 'src/third-party/activeMQ/activeMQ.service';
import { ReminderCronJob } from './reminder-cron.service';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ReminderSettings.name, schema: RemindersSettingsSchema },
      { name: NotificationToken.name, schema: NotificationTokenSchema },
      { name: NotificationMessage.name, schema: NotificationMessageSchema },
    ]),
    ConfigModule,
  ],
  providers: [ReminderCronJob, ActiveMQService],
  exports: [ActiveMQService],
})
export class CronjobsModule {}
