import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ActiveMQService } from 'src/third-party/activeMQ/activeMQ.service';
import { ReminderSettings } from 'models/reminders';
import { NotificationToken } from 'models/notificationToken';
import {
  NOTIFICATION_MESSAGE_STATUS,
  NotificationMessage,
} from 'models/notificationToken/notification-messages.schema';
import { getRandomReminderMessage } from '../utils/reminders/randomCustomMessageArray';
import { REMINDER_CATEGORY_ENUM } from 'src/user/reminders/reminders-utils.service';
import { dummyReminderMessages } from '../utils/reminders/randomCustomMessageArray';

// Add constant for dummy reminder category
const DUMMY_REMINDER_CATEGORY = 'FOLLOW_UP_MASSAGE';

@Injectable()
export class ReminderCronJob {
  constructor(
    @InjectModel(ReminderSettings.name)
    private readonly reminderModel: Model<ReminderSettings>,

    @InjectModel(NotificationToken.name)
    private readonly notificationTokenModel: Model<NotificationToken>,

    @InjectModel(NotificationMessage.name)
    private readonly notificationMessageModel: Model<NotificationMessage>,

    private readonly activeMqService: ActiveMQService,
  ) {}

  @Cron('*/15 * * * *')
  async sendReminderNotifications() {
    const nowUTC = new Date();
    const startTime = new Date(nowUTC.getTime() + 0 * 60000);
    const endTime = new Date(nowUTC.getTime() + 20 * 60000);

    const startMinutes = this.extractMinutes(startTime);
    const endMinutes = this.extractMinutes(endTime);

    const reminders = await this.getRelevantReminders(startMinutes, endMinutes);

    for (const reminder of reminders) {
      await this.processReminder(reminder, nowUTC);
    }
  }

  private async getRelevantReminders(startMinutes: number, endMinutes: number) {
    return this.reminderModel.aggregate([
      {
        $addFields: {
          totalMinutes: {
            $add: [
              { $multiply: [{ $hour: '$time' }, 60] },
              { $minute: '$time' },
            ],
          },
        },
      },
      {
        $match: {
          isDeleted: false,
          totalMinutes: { $gte: startMinutes, $lt: endMinutes },
        },
      },
      {
        $project: {
          totalMinutes: 0,
        },
      },
    ]);
  }

  private async processReminder(reminder: ReminderSettings, nowUTC: Date) {
    const notificationTokens = await this.notificationTokenModel.find({
      userId: reminder.userId,
      isNotificationActive: true,
      isActive: true,
    });

    if (!notificationTokens.length) {
      return;
    }

    for (const notificationToken of notificationTokens) {
      await this.sendNotification(reminder, notificationToken, nowUTC);
    }
  }

  private async sendNotification(
    reminder: ReminderSettings,
    token: NotificationToken,
    nowUTC: Date,
  ) {
    const actualReminderTime = this.getReminderScheduledTime(reminder.time);

    const delay = Math.max(actualReminderTime.getTime() - nowUTC.getTime(), 0);

    const baseFields = {
      userId: reminder.userId,
      notificationTokenId: token.id,
      reminderSettingsId: reminder._id,
      status: NOTIFICATION_MESSAGE_STATUS.PENDING,
      isDeleted: false,
    };

    const actualFilter = {
      ...baseFields,
      category: reminder.categoryString,
      label: reminder.label,
      isDummyReminder: false,
    };

    // === ACTUAL REMINDER ===
    try {
      const msg = await this.notificationMessageModel
        .findOne({
          ...actualFilter,
          status: {
            $in: [
              NOTIFICATION_MESSAGE_STATUS.PENDING,
              NOTIFICATION_MESSAGE_STATUS.SUCCESS,
            ],
          },
        })
        .sort({ queuedAt: -1 });

      if (
        msg &&
        msg.queuedAt &&
        msg.queuedAt >= new Date(Date.now() - 20 * 60 * 1000)
      ) {
        return;
      }

      const actualMsg = await this.notificationMessageModel.findOneAndUpdate(
        actualFilter,
        {
          $setOnInsert: {
            messageTitle: reminder.label,
            messageBody: getRandomReminderMessage(reminder.categoryString),
            sound: reminder.sound,
            frontend_screen_url: reminder.frontend_screen_url,
            time: reminder.time,
          },
          $set: {
            isQueued: true,
            queuedAt: new Date(),
            scheduledAt: actualReminderTime,
          },
        },
        { upsert: true, new: true },
      );

      console.log(
        `[ReminderCron] Queuing actual reminder with ${delay}ms delay:`,
        JSON.stringify(actualMsg, null, 2),
      );

      this.activeMqService.sendMessageWithDelay(
        'reminder-delay-queue',
        actualMsg,
        delay,
      );
    } catch (err) {
      if (err.code === 11000) {
        console.warn('[ReminderCron] Duplicate actual reminder skipped.');
      } else {
        throw err;
      }
    }

    // === DUMMY REMINDER ===
    if (reminder.categoryString === REMINDER_CATEGORY_ENUM.FOOD) {
      const dummyLabel = `${reminder.label} - follow up`;
      const dummyScheduledTime = new Date(
        actualReminderTime.getTime() + 10 * 60 * 1000,
      );

      const effectiveDummyDelay = Math.max(
        dummyScheduledTime.getTime() - nowUTC.getTime(),
        0,
      );

      const dummyFilter = {
        ...baseFields,
        category: DUMMY_REMINDER_CATEGORY,
        label: dummyLabel,
        isDummyReminder: true,
      };

      try {
        const dummyMsg = await this.notificationMessageModel.findOneAndUpdate(
          dummyFilter,
          {
            $setOnInsert: {
              messageTitle: 'It’s time to use your massage device!',
              messageBody:
                dummyReminderMessages[
                  Math.floor(Math.random() * dummyReminderMessages.length)
                ],
              sound: reminder.sound,
              frontend_screen_url: reminder.frontend_screen_url,
              time: reminder.time,
            },
            $set: {
              isQueued: true,
              queuedAt: new Date(),
              scheduledAt: dummyScheduledTime,
            },
          },
          { upsert: true, new: true },
        );

        this.activeMqService.sendMessageWithDelay(
          'reminder-delay-queue',
          dummyMsg,
          effectiveDummyDelay,
        );
      } catch (err) {
        if (err.code === 11000) {
          console.warn('[ReminderCron] Duplicate dummy reminder skipped.');
        } else {
          throw err;
        }
      }
    }
  }

  private extractMinutes(date: Date) {
    const minutes = date.getUTCHours() * 60 + date.getUTCMinutes();
    return minutes;
  }

  private getReminderScheduledTime(reminder: Date): Date {
    // today’s UTC date
    const now = new Date();
    const y = now.getUTCFullYear();
    const m = now.getUTCMonth();
    const d = now.getUTCDate();

    // reminder’s UTC time
    const h = reminder.getUTCHours();
    const min = reminder.getUTCMinutes();
    const s = reminder.getUTCSeconds();
    const ms = reminder.getUTCMilliseconds();

    // build a UTC timestamp
    return new Date(Date.UTC(y, m, d, h, min, s, ms));
  }
}
