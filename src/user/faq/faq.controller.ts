import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { FaqService } from './faq.service';
import { GetAllFaqQueryInterface } from './interfaces';
import { ErrorResponse } from 'src/utils/responses';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetAllUserFaqResDto, GetSingleFaqByIdResDto } from './dto';
import { AuthGuard } from 'src/middlewares';

@ApiTags('User-FAQs')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('faq')
export class FaqController {
  constructor(private readonly faqService: FaqService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the all faqs list.',
    type: GetAllUserFaqResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async GetAllFaqForUser(@Query() queryFilters: GetAllFaqQueryInterface) {
    return this.faqService.getAllFaqForUser(queryFilters);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved single faq.',
    type: GetSingleFaqByIdResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get(':id')
  async GetSingleFaqById(@Param('id') id: string) {
    return this.faqService.getSingleFaqById(id);
  }
}
