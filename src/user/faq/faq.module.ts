import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { FaqController } from './faq.controller';
import { FaqService } from './faq.service';
import { Faq, FaqSchema } from 'models/faq';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Faq.name, schema: FaqSchema }]),
    AuthModule,
    RepoModule,
    CommonModule,
  ],
  controllers: [FaqController],
  providers: [FaqService],
})
export class FaqModule {}
