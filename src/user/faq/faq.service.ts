import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Faq } from 'models/faq';
import { GetAllFaqQueryInterface } from './interfaces';
import { UtilsService } from 'src/common/services';
import { FaqDto, GetAllUserFaqResDto, GetSingleFaqByIdResDto } from './dto';

@Injectable()
export class FaqService {
  constructor(
    @InjectModel(Faq.name)
    private readonly faqModel: Model<Faq>,
    private readonly utilsService: UtilsService,
  ) {}

  async getAllFaqForUser(
    queryFilters: GetAllFaqQueryInterface,
  ): Promise<GetAllUserFaqResDto> {
    const { page, title } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    if (title) {
      query.question = { $regex: new RegExp(title, 'i') };
    }

    const FAQs = await this.faqModel
      .find(query)
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.faqModel.countDocuments(query);

    const resp = FAQs.map((item) => FaqDto.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: resp.length,
      faqs: resp,
    };
  }

  async getSingleFaqById(id: string): Promise<GetSingleFaqByIdResDto> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException(`Invalid FAQ ID: ${id}`);
    }

    const faq = await this.faqModel
      .findOne({ _id: new Types.ObjectId(id), isDeleted: false })
      .exec();

    if (!faq) {
      throw new NotFoundException(`FAQ with ID ${id} not found`);
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      faq: FaqDto.transform(faq),
    };
  }
}
