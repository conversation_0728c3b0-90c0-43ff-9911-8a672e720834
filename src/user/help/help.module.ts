import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { HelpController } from './help.controller';
import { HelpService } from './help.service';
import { Help, HelpSchema } from 'models/help';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Help.name, schema: HelpSchema }]),
    AuthModule,
    RepoModule,
    CommonModule,
  ],
  controllers: [HelpController],
  providers: [HelpService],
})
export class HelpModule {}
