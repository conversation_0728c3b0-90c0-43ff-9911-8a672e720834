import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { HelpService } from './help.service';
import { GetAllHelpQueryInterface } from './interfaces';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { GetSingleHelpByIdResDto, GetAllHelpResDto } from './dto';
import { AuthGuard } from 'src/middlewares';

@ApiTags('User-Help')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('help')
export class HelpController {
  constructor(private readonly helpService: HelpService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the all Help list.',
    type: GetAllHelpResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async GetAllHelps(@Query() queryFilters: GetAllHelpQueryInterface) {
    return this.helpService.getAllHelps(queryFilters);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the single Help.',
    type: GetSingleHelpByIdResDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get(':id')
  async GetHelpById(@Param('id') id: string) {
    return this.helpService.getHelpById(id);
  }
}
