import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { HelpDto } from './help.dto';

export class GetAllHelpResDto extends BaseResponse {
  @ApiProperty({ example: 100, description: 'Total number of activity videos' })
  total: number;

  @ApiProperty({
    example: 50,
    description: 'Number of hits matching the query',
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of helps',
    type: [HelpDto],
  })
  data: HelpDto[];
}
