import { ApiProperty } from '@nestjs/swagger';
import { Help } from 'models/help';
import { CATEGORY_TYPES } from 'models/help/help.schema';

class HelpTopicDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;
}

export class HelpDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  category: CATEGORY_TYPES;

  @ApiProperty({ type: [HelpTopicDto] })
  topics: HelpTopicDto[];

  @ApiProperty()
  isDeleted: boolean;

  static transform(help: Help): HelpDto {
    return {
      id: help.id,
      category: help.category,

      topics: help.topics.map((t) => ({
        id: t.id,
        title: t.title,
        description: t.description,
      })),
      isDeleted: help.isDeleted,
    };
  }
}
