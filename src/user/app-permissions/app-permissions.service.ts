import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { AppPermission, User } from 'models/user';
import {
  GetUserAppPermissionsResDTO,
  UpdateUserAppPermissionsDto,
  UpdateUserAppPermissionsResponseDto,
  UserAppPermissionDto,
} from './dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class AppPermissionsService {
  constructor(
    @InjectModel(AppPermission.name)
    private userAppPermissionModel: Model<AppPermission>,
  ) {}

  async getUserAppPermissions(
    user: User,
  ): Promise<GetUserAppPermissionsResDTO> {
    const ids = user.app_permissions.map((permission) => permission._id);

    const appPermissions = await this.userAppPermissionModel.find({
      _id: { $in: ids },
    });

    const resp = appPermissions.map((item) =>
      UserAppPermissionDto.transform(item),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      appPermissions: resp,
    };
  }

  async updateUserAppPermissions(
    user: User,
    updatePermissionsDto: UpdateUserAppPermissionsDto,
  ): Promise<UpdateUserAppPermissionsResponseDto> {
    const { app_permission, isPermissionAllowed } = updatePermissionsDto;

    if (!app_permission || isPermissionAllowed === undefined) {
      throw new BadRequestException({
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message:
          'Permission name is required, and permission status must be specified.',
      });
    }

    const updatedResult = await this.userAppPermissionModel.updateOne(
      { _id: user.app_permissions },
      { $set: { isPermissionAllowed, app_permission } },
    );

    if (updatedResult.modifiedCount === 0) {
      throw new BadRequestException({
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message:
          'No changes were made. Either the record does not exist or the values are the same.',
      });
    }

    // Dynamic response message
    const data = isPermissionAllowed
      ? 'App permission updated successfully.'
      : 'App permission removed successfully.';

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data,
    };
  }
}
