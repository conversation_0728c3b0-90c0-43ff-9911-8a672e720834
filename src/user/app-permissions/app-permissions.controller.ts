import { Body, Controller, Get, Put, Req, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { AppPermissionsService } from './app-permissions.service';
import { Request } from 'express';
import { ErrorResponse } from 'src/utils/responses';
import {
  GetUserAppPermissionsResDTO,
  UpdateUserAppPermissionsDto,
} from './dto';

@ApiTags('User-App-Permissions')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('app_permissions')
export class AppPermissionsController {
  constructor(private readonly appPermissionService: AppPermissionsService) {}

  @Get()
  @ApiOperation({ summary: 'Get user app permissions' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user app permissions.',
    type: GetUserAppPermissionsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Permissions not found or user not found.',
    type: ErrorResponse,
  })
  async getUserAppPermissions(@Req() req: Request) {
    const user = req['user'];
    return this.appPermissionService.getUserAppPermissions(user);
  }

  @Put('update')
  @ApiOperation({ summary: 'Update user app permissions' })
  @ApiResponse({
    status: 200,
    description: 'Successfully updated user app permissions.',
    type: GetUserAppPermissionsResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data.',
    type: ErrorResponse,
  })
  async updateUserAppPermissions(
    @Req() req: Request,
    @Body() updatePermissionsDto: UpdateUserAppPermissionsDto,
  ) {
    return this.appPermissionService.updateUserAppPermissions(
      req['user'],
      updatePermissionsDto,
    );
  }
}
