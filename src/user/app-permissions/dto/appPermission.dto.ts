import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsBoolean, IsArray, IsString } from 'class-validator';
import { AppPermission, USER_APP_PERMISSIONS } from 'models/user';

export class UserAppPermissionDto {
  @ApiProperty({
    description: 'The provider name (APPLE or GOOGLE)',
    enum: USER_APP_PERMISSIONS,
    example: USER_APP_PERMISSIONS.APPLE,
  })
  @IsEnum(USER_APP_PERMISSIONS, {
    message: 'provider_name must be either apple or google',
  })
  app_permission: USER_APP_PERMISSIONS;

  @ApiProperty({
    description: 'Indicates whether the permission is allowed',
    example: false,
  })
  @IsBoolean({ message: 'isPermissionAllowed must be a boolean value' })
  isPermissionAllowed: boolean;

  @ApiProperty({
    description:
      'List of other user permissions like step-count, heartbeat-count, etc.',
    type: [String],
    example: ['step-count', 'heartbeat-count'],
  })
  @IsArray()
  @IsString({ each: true, message: 'Each permission must be a string' })
  otherPermissions: string[];

  static transform(object: AppPermission): UserAppPermissionDto {
    const transformedObj: UserAppPermissionDto = new UserAppPermissionDto();

    transformedObj.app_permission = object.app_permission;
    transformedObj.isPermissionAllowed = object.isPermissionAllowed;
    transformedObj.otherPermissions = object.otherPermissions || [];

    return transformedObj;
  }
}
