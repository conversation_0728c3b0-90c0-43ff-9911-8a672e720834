import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsBoolean, IsNotEmpty } from 'class-validator';
import { USER_APP_PERMISSIONS } from 'models/user';

export class UpdateUserAppPermissionsDto {
  @ApiProperty({
    description: 'The provider name (APPLE or GOOGLE)',
    enum: USER_APP_PERMISSIONS,
    example: USER_APP_PERMISSIONS.APPLE,
  })
  @IsEnum(USER_APP_PERMISSIONS, {
    message: 'provider_name must be either APPLE or GOOGLE',
  })
  @IsNotEmpty({ message: 'provider_name is required' })
  app_permission: USER_APP_PERMISSIONS;

  @ApiProperty({
    description: 'Indicates whether the permission is allowed',
    example: false,
  })
  @IsBoolean({ message: 'isPermissionAllowed must be a boolean value' })
  @IsNotEmpty({ message: 'isPermissionAllowed is required' })
  isPermissionAllowed: boolean;
}
