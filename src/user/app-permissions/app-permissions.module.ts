import { Modu<PERSON> } from '@nestjs/common';
import { AppPermissionsService } from './app-permissions.service';
import { AppPermissionsController } from './app-permissions.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { AppPermission, AppPermissionSchema } from 'models/user';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AppPermission.name, schema: AppPermissionSchema },
    ]),
    AuthModule,
    RepoModule,
    CommonModule,
  ],
  providers: [AppPermissionsService],
  controllers: [AppPermissionsController],
})
export class AppPermissionsModule {}
