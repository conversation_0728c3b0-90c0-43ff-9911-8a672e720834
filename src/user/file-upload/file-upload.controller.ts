import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Get,
  Param,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileUploadService } from './file-upload.service';
import { Express } from 'express';

@Controller('file')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    const uploaded = await this.fileUploadService.uploadAndStoreFile(file);
    return {
      message: 'File uploaded and scanned successfully',
      fileId: uploaded._id,
    };
  }

  @Get(':id/url')
  async getFileUrl(@Param('id') fileId: string) {
    const url = await this.fileUploadService.getValidS3Url(fileId);
    return { url };
  }
}
