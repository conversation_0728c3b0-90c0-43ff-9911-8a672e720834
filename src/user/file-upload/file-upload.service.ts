import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as dayjs from 'dayjs';

import { AwsS3Service } from 'src/third-party/aws';
import { UserFileUploads } from 'models/file-upload/file-upload.schema';
import { ClamAVService } from './clamav.service';

@Injectable()
export class FileUploadService {
  constructor(
    private readonly awsS3Service: AwsS3Service,
    private readonly clamAVService: ClamAVService,
    @InjectModel(UserFileUploads.name)
    private readonly fileUploadModel: Model<UserFileUploads>,
  ) {}

  async scanWithClamAV(file: Express.Multer.File): Promise<void> {
    const hasVirus = await this.clamAVService.scanBuffer(file.buffer);
    if (hasVirus) throw new BadRequestException('File contains a virus.');
  }

  async uploadAndStoreFile(
    file: Express.Multer.File,
  ): Promise<UserFileUploads> {
    await this.scanWithClamAV(file); // Step 1: Scan

    const s3Result = await this.awsS3Service.uploadFile(file); // Step 2: Upload

    const expiry = dayjs().add(1, 'day').toDate();

    const fileDoc = await this.fileUploadModel.create({
      fileName: file.originalname,
      fileType: file.mimetype,
      size: file.size,
      s3Key: s3Result.Key,
      s3Url: s3Result.Location,
      expiryAt: expiry,
      isExpired: false,
    });

    return fileDoc;
  }

  async getValidS3Url(fileId: string): Promise<string> {
    const file = await this.fileUploadModel.findById(fileId);
    if (!file) throw new NotFoundException('File not found');

    const now = new Date();
    if (now < file.expiryAt && file.s3Url && !file.isExpired) {
      return file.s3Url;
    }

    const newUrl = await this.awsS3Service.getSignedUrl(file.s3Key);
    const newExpiry = dayjs().add(1, 'day').toDate();

    file.s3Url = newUrl;
    file.expiryAt = newExpiry;
    file.isExpired = false;
    await file.save();

    return newUrl;
  }
}
