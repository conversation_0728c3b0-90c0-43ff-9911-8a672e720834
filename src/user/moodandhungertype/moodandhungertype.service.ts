import { Injectable } from '@nestjs/common';
import { HUNGER_LEVELS, MOOD_TYPES } from 'models/user-records/Mood-Records';
import {
  GetHungerLevelsResDTO,
  GetMoodTypesResDTO,
} from './dto/get-mood-and-hunger-types.res.dto';

@Injectable()
export class MoodAndHungerTypeService {
  getMoodTypes(): GetMoodTypesResDTO {
    return {
      error: false,
      statusCode: 200,
      data: Object.values(MOOD_TYPES),
    };
  }

  getHungerLevels(): GetHungerLevelsResDTO {
    return {
      error: false,
      statusCode: 200,
      data: Object.values(HUNGER_LEVELS),
    };
  }
}
