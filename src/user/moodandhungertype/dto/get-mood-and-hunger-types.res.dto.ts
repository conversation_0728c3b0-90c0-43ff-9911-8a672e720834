import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses/baseResponse';

export class GetMoodTypesResDTO extends BaseResponse {
  @ApiProperty({
    description: 'List of mood types',
    example: ['Happy', 'Sad', 'Angry'],
    type: [String],
  })
  data: string[];
}

export class GetHungerLevelsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'List of hunger levels',
    example: ['Not Hungry', 'Slightly Hungry', 'Very Hungry'],
    type: [String],
  })
  data: string[];
}
