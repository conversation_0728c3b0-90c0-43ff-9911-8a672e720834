import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MoodAndHungerTypeService } from './moodandhungertype.service';
import { AuthGuard } from 'src/middlewares';
import {
  GetMoodTypesResDTO,
  GetHungerLevelsResDTO,
} from './dto/get-mood-and-hunger-types.res.dto';

@UseGuards(AuthGuard)
@ApiTags('Mood & Hunger Types')
@Controller()
export class MoodAndHungerTypeController {
  constructor(
    private readonly moodAndHungerTypeService: MoodAndHungerTypeService,
  ) {}

  @Get('mood-types')
  @ApiOperation({ summary: 'Get all mood types' })
  @ApiResponse({ status: 200, type: GetMoodTypesResDTO })
  getMoodTypes(): GetMoodTypesResDTO {
    return this.moodAndHungerTypeService.getMoodTypes();
  }

  @Get('hunger-levels')
  @ApiOperation({ summary: 'Get all hunger levels' })
  @ApiResponse({ status: 200, type: GetHungerLevelsResDTO })
  getHungerLevels(): GetHungerLevelsResDTO {
    return this.moodAndHungerTypeService.getHungerLevels();
  }
}
