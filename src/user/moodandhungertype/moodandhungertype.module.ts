import { Module } from '@nestjs/common';
import { MoodAndHungerTypeController } from './moodandhungertype.controller';
import { MoodAndHungerTypeService } from './moodandhungertype.service';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';
import { CommonModule } from 'src/common/common.module';
import {
  UserMoodRecords,
  UserMoodRecordsSchema,
} from 'models/user-records/Mood-Records';
import { MongooseModule } from '@nestjs/mongoose';
import { ThirdPartyModule } from 'src/third-party/third-party.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserMoodRecords.name, schema: UserMoodRecordsSchema },
    ]),
    AuthModule,
    RepoModule,
    CommonModule,
    ThirdPartyModule,
  ],
  controllers: [MoodAndHungerTypeController],
  providers: [MoodAndHungerTypeService],
})
export class MoodAndHungerTypeModule {}
