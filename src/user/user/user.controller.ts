import { Body, Controller, Get, Put, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { UserService } from './user.service';
import { Request } from 'express';
import {
  CompleteUserProfileReqDTO,
  CompleteUserProfileResDTO,
  GetUserProfileResDTO,
  UpdateUserProfileReqDTO,
  UpdateUserProfileResDTO,
  UpdateUserTimeZoneReqDTO,
  UpdateUserTimeZoneResDTO,
} from './user-dto';
import { AuthGuard } from 'src/middlewares';

@ApiTags('User')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user profile.',
    type: GetUserProfileResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/profile')
  async getUserProfile(@Req() req: Request) {
    const user = req['user'];

    return this.userService.getUserProfile(user);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully completed the user profile.',
    type: CompleteUserProfileResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('/profile/setup_profile')
  async completeUserProfile(
    @Body() completeUserProfileData: CompleteUserProfileReqDTO,
    @Req() req: Request,
  ) {
    const user = req['user'];

    return this.userService.completeUserProfile(user, completeUserProfileData);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully updated the user profile.',
    type: UpdateUserProfileResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('/profile')
  async updateUserProfile(
    @Body() updateUserProfileData: UpdateUserProfileReqDTO,
    @Req() req: Request,
  ) {
    const user = req['user'];

    return this.userService.updateUserProfile(user, updateUserProfileData, req);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully updated the user TimeZone.',
    type: UpdateUserTimeZoneResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('/timezone_update')
  async updateUserTimeZone(
    @Body() updateUserTimeZoneData: UpdateUserTimeZoneReqDTO,
    @Req() req: Request,
  ) {
    const user = req['user'];

    return this.userService.updateUserTimeZone(user, updateUserTimeZoneData);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user status.',
    type: GetUserProfileResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/status')
  async getUserStatus(@Req() req: Request) {
    const user = req['user'];
    return this.userService.getUserStatus(user);
  }
}
