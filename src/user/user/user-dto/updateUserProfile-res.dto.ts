import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { UserProfileDto } from './userProfile.dto';

export class UpdateUserProfileResDTO extends BaseResponse {
  @ApiProperty({
    type: String,
    description: 'success message',
    example: 'User Updated Successfully !!',
  })
  msg: string;

  @ApiProperty({
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
