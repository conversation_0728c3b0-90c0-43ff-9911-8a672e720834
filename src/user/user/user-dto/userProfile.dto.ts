import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNumber,
  IsO<PERSON>al,
  <PERSON>,
  <PERSON>,
  ValidateIf,
} from 'class-validator';
import { AppPermission, GENDER_TYPES, USER_APP_PERMISSIONS } from 'models/user';
import { DIET_PREFERENCE, User } from 'models/user/user.schema';
import { UserProfileGoalDTO } from './userProfileGoal.dto';

export class UserProfileDto {
  @ApiProperty({
    example: '123',
  })
  id: string;

  @ApiProperty({
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isEmailVerified: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isDeleted: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isAccountCompleted: boolean;

  @ApiProperty({
    example: 22,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  age?: number;

  @ApiProperty({
    example: 'male',
    required: false,
  })
  @IsOptional()
  @IsEnum(GENDER_TYPES)
  gender?: GENDER_TYPES;

  @ApiProperty({
    example: 170,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    example: 70,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  weight?: number;

  @ApiProperty({
    example: 'veg',
    required: false,
  })
  @IsOptional()
  @IsEnum(DIET_PREFERENCE)
  diet_preference?: DIET_PREFERENCE;

  @ApiProperty({
    example: [],
    required: false,
  })
  goals?: UserProfileGoalDTO[];

  @ApiProperty({
    example: 'America/Los_Angeles',
  })
  timeZone: string;

  @ApiProperty({
    example: 2,
    description: 'Device usage limit (must be between 2 and 3) or null',
    minimum: 2,
    maximum: 3,
    nullable: true,
  })
  @ValidateIf(
    (obj) =>
      obj.deviceUsageLimit !== null && obj.deviceUsageLimit !== undefined,
  )
  @IsNumber()
  @Min(2)
  @Max(3)
  @IsOptional()
  deviceUsageLimit: number | null;

  @ApiProperty({
    example: [{ app_permission: 'google', isPermissionAllowed: true }],
    required: false,
  })
  @IsOptional()
  app_permissions?: {
    app_permission: USER_APP_PERMISSIONS;
    isPermissionAllowed: boolean;
  }[];

  static transform(object: User): UserProfileDto {
    const transformedObj: UserProfileDto = new UserProfileDto();

    transformedObj.id = object._id.toString();
    transformedObj.email = object.email;
    transformedObj.name = object.name;
    transformedObj.isEmailVerified = object.isEmailVerified;
    transformedObj.isAccountCompleted = object.isAccountCompleted;
    transformedObj.isDeleted = object.isDeleted;

    transformedObj.age = object.age;
    transformedObj.gender = object.gender;
    transformedObj.height = object.height;
    transformedObj.weight = object.weight;
    transformedObj.deviceUsageLimit = object.deviceUsageLimit;
    transformedObj.diet_preference = object.diet_preference;
    transformedObj.timeZone = object.timeZone || null;

    transformedObj.goals = object.goals.map((item) =>
      UserProfileGoalDTO.transform(item),
    );

    if (object.app_permissions?.length) {
      transformedObj.app_permissions = (
        object.app_permissions as unknown as AppPermission[]
      ).map((perm) => ({
        app_permission: perm.app_permission,
        isPermissionAllowed: perm.isPermissionAllowed,
      }));
    }

    return transformedObj;
  }
}
