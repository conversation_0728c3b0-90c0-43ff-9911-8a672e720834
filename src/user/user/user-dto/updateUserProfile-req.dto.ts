import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsEmail,
  IsInt,
  IsString,
  Min,
  Max,
  IsOptional,
  IsEnum,
  IsNumber,
  MinLength,
} from 'class-validator';
import { DIET_PREFERENCE } from 'models/user/user.schema';

export class UpdateUserProfileReqDTO {
  @ApiProperty({
    description: 'The name of the user',
    type: String,
  })
  @MinLength(3, {
    message: 'Please enter a valid name with at least 3 characters',
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    description: 'The email address of the user',
    type: String,
  })
  @IsEmail()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase(), { toClassOnly: true })
  email: string;

  @ApiProperty({
    description: 'The age of the user',
    type: Number,
  })
  @IsInt()
  @Min(3, {
    message: 'The age you entered is outside the acceptable range (3-130).',
  })
  @Max(130, {
    message: 'The age you entered is outside the acceptable range (3-130).',
  })
  @IsOptional()
  age: number;

  @ApiProperty({
    description: 'The height of the user in centimeters',
    type: Number,
  })
  @IsInt()
  @Min(50, {
    message:
      'The height you entered is outside the acceptable range (50-500 cm).',
  })
  @Max(500, {
    message:
      'The height you entered is outside the acceptable range (50-500 cm).',
  })
  @IsOptional()
  height: number;

  @ApiProperty({
    description: 'The weight of the user in kilograms',
    type: Number,
  })
  @IsNumber()
  @Min(5, {
    message:
      'The Weight you entered is outside the acceptable range (5-400 kg).',
  })
  @Max(400, {
    message:
      'The Weight you entered is outside the acceptable range (5-400 kg)',
  })
  @IsOptional()
  weight: number;

  @ApiProperty({
    description: 'Diet preference of the user (e.g., vegetarian, vegan, etc.)',
    type: String,
  })
  @IsEnum(DIET_PREFERENCE, { message: 'Please provide valid diet preference' })
  @IsOptional()
  diet_preference: DIET_PREFERENCE;
}
