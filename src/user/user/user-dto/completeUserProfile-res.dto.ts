import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { UserProfileDto } from './userProfile.dto';
import { IsOptional } from 'class-validator';
import { CreateDeviceConnectionResDTO } from 'src/user/device/device-dto';

export class CompleteUserProfileResDTO extends BaseResponse {
  @ApiProperty({
    type: String,
    description: 'success message',
    example: 'User Profile Completed !!',
  })
  msg: string;

  @ApiProperty({
    type: UserProfileDto,
  })
  user: UserProfileDto;

  @ApiProperty({
    type: CreateDeviceConnectionResDTO,
  })
  @IsOptional()
  device?: CreateDeviceConnectionResDTO;
}
