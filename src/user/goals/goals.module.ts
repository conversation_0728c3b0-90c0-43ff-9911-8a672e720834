import { Modu<PERSON> } from '@nestjs/common';
import { GoalsService } from './goals.service';
import { GoalsController } from './goals.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { UserGoal, UserGoalSchema } from 'models/user';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserGoal.name, schema: UserGoalSchema },
    ]),
    CommonModule,
    AuthModule,
    RepoModule,
  ],
  providers: [GoalsService],
  controllers: [GoalsController],
})
export class GoalsModule {}
