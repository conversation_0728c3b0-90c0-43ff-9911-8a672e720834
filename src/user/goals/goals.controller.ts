import { Body, Controller, Get, Put, Req, UseGuards } from '@nestjs/common';
import { GoalsService } from './goals.service';
import { UpdateUserGoalsReqDTO, UpdateUserGoalsResDTO } from './dto';
import { ErrorResponse } from 'src/utils/responses';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { GetUserGoalsResDTO } from './dto/getUserGoals-res.dto';

@ApiTags('User-Goals')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('goals')
export class GoalsController {
  constructor(private readonly goalsService: GoalsService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully updated the user profile.',
    type: UpdateUserGoalsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put()
  async updateUserGoals(
    @Body() updateUserGoalsData: UpdateUserGoalsReqDTO,
    @Req() req: Request,
  ) {
    const user = req['user'];

    return this.goalsService.updateUserGoals(user, updateUserGoalsData);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user goals.',
    type: GetUserGoalsResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async getUserGoals(@Req() req: Request) {
    const user = req['user'];
    return this.goalsService.getUserGoals(user);
  }
}
