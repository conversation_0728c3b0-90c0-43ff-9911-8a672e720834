import { HttpStatus, Injectable } from '@nestjs/common';
import { GOAL_TYPES, User } from 'models/user';
import { UpdateUserGoalsReqDTO, UpdateUserGoalsResDTO } from './dto';
import { UserRepoService } from 'src/repo/user-repo.service';
import { GetUserGoalsResDTO, UserGoalsData } from './dto/getUserGoals-res.dto';
import { UserProfileDto, UserProfileGoalDTO } from '../user/user-dto';

@Injectable()
export class GoalsService {
  constructor(private readonly userRepo: UserRepoService) {}

  async updateUserGoals(
    user: User,
    updateUserGoalsData: UpdateUserGoalsReqDTO,
  ): Promise<UpdateUserGoalsResDTO> {
    const {
      activity_goal,
      mind_goal,
      physical_goal,
      sleep_goal,
      deviceUsageLimit,
    } = updateUserGoalsData;
    const updateData: any = {};
    // Update deviceUsageLimit separately
    if (deviceUsageLimit !== undefined) {
      updateData.deviceUsageLimit = deviceUsageLimit;
    }
    if (user.goals) {
      const updatedGoals = user.goals.map((goal) => {
        const updatedGoal = JSON.parse(JSON.stringify(goal)); // Deep copy to avoid reference issues
        if (
          goal.goal_type === GOAL_TYPES.MOVEMENT &&
          activity_goal !== undefined &&
          activity_goal !== ''
        ) {
          updatedGoal.selected_goal = activity_goal;
        } else if (
          goal.goal_type === GOAL_TYPES.MINDFULNESS &&
          mind_goal !== undefined &&
          mind_goal !== ''
        ) {
          updatedGoal.selected_goal = mind_goal;
        } else if (
          goal.goal_type === GOAL_TYPES.PHYSICAL &&
          physical_goal !== undefined &&
          physical_goal !== ''
        ) {
          updatedGoal.selected_goal = physical_goal;
        } else if (
          goal.goal_type === GOAL_TYPES.SLEEP &&
          sleep_goal !== undefined &&
          sleep_goal !== ''
        ) {
          updatedGoal.selected_goal = sleep_goal;
        }
        return updatedGoal;
      });

      // Ensure goals actually get updated
      if (JSON.stringify(updatedGoals) !== JSON.stringify(user.goals)) {
        updateData.goals = updatedGoals;
      }
    }
    // If nothing changed, return early
    if (Object.keys(updateData).length === 0) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'No changes detected.',
        user: UserProfileDto.transform(user),
      };
    }
    // Update the user in the database
    const updatedUser = await this.userRepo.findUserByIdAndUpdate(
      user._id.toString(),
      updateData,
    );
    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'User goals updated successfully!',
      user: UserProfileDto.transform(updatedUser),
    };
  }
  async getUserGoals(user: User): Promise<GetUserGoalsResDTO> {
    const goals = user.goals || [];
    const resp: UserGoalsData = {
      goals: goals.map((item) => UserProfileGoalDTO.transform(item)),
      deviceUsageLimit: user.deviceUsageLimit,
    };
    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
