import { ApiProperty } from '@nestjs/swagger';
import {
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateIf,
} from 'class-validator';

export class UpdateUserGoalsReqDTO {
  @ApiProperty({
    description: 'The physical goal of user',
    type: String,
  })
  @IsString()
  @IsOptional()
  physical_goal: string;

  @ApiProperty({
    description: 'The activity goal of user',
    type: String,
  })
  @IsString()
  @IsOptional()
  activity_goal: string;

  @ApiProperty({
    description: 'The mind goal of user',
    type: String,
  })
  @IsString()
  @IsOptional()
  mind_goal: string;

  @ApiProperty({
    description: 'The sleep goal of user',
    type: String,
  })
  @IsString()
  @IsOptional()
  sleep_goal: string;

  @ApiProperty({
    example: 2,
    description: 'Device usage limit (must be between 2 and 3) or null',
    minimum: 2,
    maximum: 3,
    nullable: true,
  })
  @ValidateIf(
    (obj) =>
      obj.deviceUsageLimit !== null && obj.deviceUsageLimit !== undefined,
  )
  @IsNumber()
  @Min(2)
  @Max(3)
  @IsOptional()
  deviceUsageLimit: number | null;
}
