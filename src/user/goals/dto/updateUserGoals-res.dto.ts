import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { UserProfileDto } from '../../user/user-dto/userProfile.dto';

export class UpdateUserGoalsResDTO extends BaseResponse {
  @ApiProperty({
    type: String,
    description: 'success message',
    example: 'User Goals Updated Successfully !!',
  })
  msg: string;

  @ApiProperty({
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
