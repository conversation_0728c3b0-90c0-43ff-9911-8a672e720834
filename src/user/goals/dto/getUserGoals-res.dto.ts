import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ValidateIf } from 'class-validator';
import { UserProfileGoalDTO } from 'src/user/user/user-dto';
import { BaseResponse } from 'src/utils/responses';
export class UserGoalsData {
  @ApiProperty({ type: [UserProfileGoalDTO] })
  goals: UserProfileGoalDTO[];
  @ApiProperty({
    example: 2,
    description: 'Device usage limit (must be between 2 and 3) or null',
    minimum: 2,
    maximum: 3,
    nullable: true,
  })
  @ValidateIf(
    (obj) =>
      obj.deviceUsageLimit !== null && obj.deviceUsageLimit !== undefined,
  )
  @IsNumber()
  @Min(2)
  @Max(3)
  deviceUsageLimit: number | null;
}
export class GetUserGoalsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'List of user goals',
    type: UserGoalsData,
  })
  data: { goals: UserProfileGoalDTO[]; deviceUsageLimit: number };
}
