import { Injectable, OnApplicationBootstrap } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ActiveMQService } from 'src/third-party/activeMQ/activeMQ.service';
import { ExpoNotificationService } from 'src/third-party/expoNotification/expoNotification.service';
import { NotificationToken } from 'models/notificationToken';
import {
  NotificationMessage,
  NOTIFICATION_MESSAGE_STATUS,
} from 'models/notificationToken/notification-messages.schema';
import { NotificationCounter } from 'models/notificationToken/notification-counter.schema';
import { ConfigService } from '@nestjs/config';
import { CustomLogger } from 'src/common/services';
import { ErrorResponse } from 'src/utils/responses';

// const DUMMY_REMINDER_CATEGORY = 'FOLLOW_UP_MASSAGE';

@Injectable()
export class ConsumerService implements OnApplicationBootstrap {
  private readonly MAX_NOTIFICATION_ATTEMPTS = 3;
  private readonly CONSUMER_REMINDER_TTL: number;

  constructor(
    private readonly activeMQService: ActiveMQService,
    private readonly expoNotificationService: ExpoNotificationService,
    @InjectModel(NotificationToken.name)
    private readonly notificationTokenModel: Model<NotificationToken>,
    @InjectModel(NotificationMessage.name)
    private readonly notificationMessageModel: Model<NotificationMessage>,
    @InjectModel(NotificationCounter.name)
    private readonly notificationCounterModel: Model<NotificationCounter>,
    private readonly configService: ConfigService,
    private readonly logger: CustomLogger,
  ) {
    this.CONSUMER_REMINDER_TTL =
      this.configService.get<number>('CONSUMER_REMINDER_TTL') || 1;
  }

  onApplicationBootstrap() {
    console.log('[ReminderConsumer] Subscribing to reminder-delay-queue...');
    this.activeMQService.subscribeToQueue(
      'reminder-delay-queue',
      async (msg) => {
        await this.handleTestMessage(msg);
      },
    );
  }

  private async handleTestMessage(msg: any) {
    try {
      const message = typeof msg === 'string' ? JSON.parse(msg) : msg;

      console.log(
        '[ReminderConsumer] Received message:',
        JSON.stringify(message, null, 2),
      );

      const existingMessage = await this.notificationMessageModel.findById(
        message._id,
      );

      if (
        !existingMessage ||
        existingMessage.isDeleted ||
        existingMessage.status !== NOTIFICATION_MESSAGE_STATUS.PENDING ||
        existingMessage.isQueued !== true
      ) {
        return;
      }

      const userToken = await this.notificationTokenModel.findById(
        existingMessage.notificationTokenId,
      );

      if (
        !userToken ||
        !userToken.isActive ||
        !userToken.isNotificationActive
      ) {
        return;
      }

      let notificationCounter = await this.notificationCounterModel.findOne({
        deviceId: userToken.deviceId,
        userId: userToken.userId,
        notificationTokenId: userToken._id,
      });

      if (!notificationCounter) {
        notificationCounter = await this.notificationCounterModel.create({
          deviceId: userToken.deviceId,
          userId: userToken.userId,
          notificationTokenId: userToken._id,
        });
      }

      if (notificationCounter.counter >= this.MAX_NOTIFICATION_ATTEMPTS) {
        userToken.isNotificationActive = false;
        userToken.isActive = false;
        await userToken.save();
        return;
      }

      try {
        await this.expoNotificationService.sendPushNotification(
          userToken.notificationToken,
          message,
        );

        existingMessage.status = NOTIFICATION_MESSAGE_STATUS.SUCCESS;
        existingMessage.isQueued = false;
        await existingMessage.save();

        notificationCounter.counter = 0;
        await notificationCounter.save();
      } catch (err) {
        const errorResponse = new ErrorResponse();
        errorResponse.message = `[ReminderConsumer] Notification sending failed: ${err}`;
        errorResponse.statusCode = 500;
        this.logger.error(errorResponse);

        existingMessage.status = NOTIFICATION_MESSAGE_STATUS.FAILED;
        existingMessage.isQueued = false;
        await existingMessage.save();

        notificationCounter.counter++;
        await notificationCounter.save();
      }
    } catch (err) {
      const errorResponse = new ErrorResponse();
      errorResponse.message = `[ReminderConsumer] Error while handling message: ${err}`;
      errorResponse.statusCode = 500;
      this.logger.error(errorResponse);
    }
  }
}
