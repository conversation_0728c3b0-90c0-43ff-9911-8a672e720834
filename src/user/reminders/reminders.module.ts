import { Module } from '@nestjs/common';
import { RemindersService } from './reminders.service';
import { RemindersController } from './reminders.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  ReminderCategory,
  ReminderCategorySchema,
  ReminderSettings,
  RemindersSettingsSchema,
} from 'models/reminders';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';
import { CommonModule } from 'src/common/common.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { Recipes, RecipesSchema } from 'models/recipe';
import { RemindersUtilsService } from './reminders-utils.service';
import {
  NotificationMessage,
  NotificationMessageSchema,
} from 'models/notificationToken/notification-messages.schema';
import { ConsumerService } from './consumer/consumer.service';
import { ConfigModule } from '@nestjs/config';
import {
  NotificationCounter,
  NotificationCounterSchema,
} from 'models/notificationToken';
import { CronjobsModule } from 'src/cronjobs/cronjobs.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Recipes.name, schema: RecipesSchema },
      { name: ReminderSettings.name, schema: RemindersSettingsSchema },
      { name: ReminderCategory.name, schema: ReminderCategorySchema },
      { name: NotificationMessage.name, schema: NotificationMessageSchema },
      { name: NotificationCounter.name, schema: NotificationCounterSchema },
    ]),
    AuthModule,
    RepoModule,
    CommonModule,
    ThirdPartyModule,
    ConfigModule,
    CronjobsModule,
  ],
  controllers: [RemindersController],
  providers: [RemindersService, RemindersUtilsService, ConsumerService],
  exports: [MongooseModule, RemindersService, RemindersUtilsService],
})
export class RemindersModule {}
