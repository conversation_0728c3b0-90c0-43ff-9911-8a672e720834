import {
  Injectable,
  HttpStatus,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ReminderSettings } from 'models/reminders';
import { Model, Types } from 'mongoose';
import { UtilsService } from 'src/common/services';
import {
  CreateReminderReqDTO,
  CreateReminderResDTO,
  GetAllReminderDTO,
  ReminderDTO,
  ReminderFoodMetaDataDTO,
} from './dto';
import {
  UpdateReminderReqDTO,
  UpdateReminderResDTO,
} from './dto/updateReminder.dto';
import { DeleteReminderDTO } from './dto/deleteReminder.dto';
import {
  CreateReminderDataInterface,
  GetAllRemindersQueryInterface,
  UpdateReminderPayloadInterface,
} from './interface';
import { GetSingleReminderResDTO } from './dto/getSingleReminder.dto';
import {
  REMINDER_CATEGORY_ENUM,
  RemindersUtilsService,
} from './reminders-utils.service';
import { NotificationMessage } from 'models/notificationToken';
import * as moment from 'moment-timezone';
import { User } from 'models/user';

@Injectable()
export class RemindersService {
  constructor(
    private readonly reminderUtilsService: RemindersUtilsService,
    private readonly utilsService: UtilsService,

    @InjectModel(User.name)
    private userModel: Model<User>,

    @InjectModel(ReminderSettings.name)
    private reminderModel: Model<ReminderSettings>,

    @InjectModel(NotificationMessage.name)
    private notificationMessageModel: Model<NotificationMessage>,
  ) {}

  async getAllReminders(
    queryFilters: GetAllRemindersQueryInterface,
    userId: string,
  ): Promise<GetAllReminderDTO> {
    const { page, category, hasMetaData } = queryFilters;

    // Use utility service to calculate limit and offset
    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { userId: new Types.ObjectId(userId), isDeleted: false };

    // Apply category filter if provided and valid
    if (category && category.trim().length > 0) {
      query.categoryString = { $regex: new RegExp(category.trim(), 'i') };
    }

    // Handle hasMetaData filter
    if (
      hasMetaData !== undefined &&
      (hasMetaData === 'true' || hasMetaData === 'false')
    ) {
      const hasMetaDataBoolVal = hasMetaData === 'true' ? true : false;

      if (hasMetaDataBoolVal) {
        // Check if metadata exists and is not an empty object
        query.metadata = { $ne: {}, $exists: true };
      } else {
        // Check if metadata is an empty object or doesn't exist
        query.$or = [{ metadata: {} }, { metadata: { $exists: false } }];
      }
    }

    const reminders = await this.reminderModel
      .find(query)
      .skip(offset)
      .limit(limit)
      .sort({ createdAt: -1 })
      .exec();

    const total = await this.reminderModel.countDocuments(query);

    const reminderResponses = reminders.map((item) =>
      ReminderDTO.transform(item),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: reminderResponses.length,
      data: reminderResponses,
    };
  }

  async getReminderById(
    id: string,
    userId: string,
  ): Promise<GetSingleReminderResDTO> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid reminder ID provided !!');
    }

    const reminder = await this.reminderModel
      .findOne({
        _id: id,
        userId: userId,
        isDeleted: false,
      })
      .exec();

    if (!reminder) {
      throw new NotFoundException('Reminder not found !!');
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: ReminderDTO.transform(reminder),
    };
  }

  async deleteReminder(id: string): Promise<DeleteReminderDTO> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid reminder ID provided !!');
    }

    try {
      const reminder = await this.reminderModel.findById(id);

      if (!reminder || reminder.isDeleted) {
        throw new NotFoundException('Reminder not found !!');
      }

      await this.notificationMessageModel.updateMany(
        {
          reminderSettingsId: reminder.id,
          isDeleted: false,
        },
        {
          isDeleted: true,
        },
      );

      reminder.isDeleted = true;
      await reminder.save();

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Reminder deleted successfully',
      };
    } catch (error) {
      // Handle MongoDB operation errors
      throw new InternalServerErrorException(
        'Failed to delete reminder',
        error.message,
      );
    }
  }

  async createReminder(
    createReminderReqDto: CreateReminderReqDTO,
    userId: string,
  ): Promise<CreateReminderResDTO> {
    const { category, frontend_screen_url, label, sound, time, metadata } =
      createReminderReqDto;

    const user = await this.userModel.findById(userId).lean();
    const userTimeZone = user?.timeZone || 'UTC';

    // Convert user time to UTC
    const UTC_Time = moment.tz(time, userTimeZone).utc();

    const existingReminder = await this.reminderModel.findOne({
      label,
      categoryString: category,
      userId,
      isDeleted: false,
    });

    if (existingReminder) {
      throw new BadRequestException(
        'Reminder with this label and category already exists !!',
      );
    }

    // Get or create category
    const { categoryId, categoryString } =
      await this.reminderUtilsService.getOrCreateCategory(category, userId);

    // Prepare reminder data
    const reminderData: CreateReminderDataInterface = {
      userId,
      categoryId,
      categoryString,
      frontend_screen_url,
      label,
      sound,
      time: UTC_Time.toDate(),
    };

    // Handle metadata if provided
    if (metadata) {
      const metadataDto = this.reminderUtilsService.getMetadataDto(
        category,
        metadata,
      );

      await this.reminderUtilsService.validateMetadata(metadataDto);

      let updatedMetaData: any;

      if (
        category === REMINDER_CATEGORY_ENUM.FOOD &&
        metadataDto instanceof ReminderFoodMetaDataDTO
      ) {
        updatedMetaData =
          await this.reminderUtilsService.validateFoodReminderMetadata(
            metadata as ReminderFoodMetaDataDTO,
            userId,
          );
      }

      reminderData.metadata = updatedMetaData;
    }

    // Save reminder to the database
    const newReminder = await this.reminderModel.create(reminderData);
    const resp = ReminderDTO.transform(newReminder);

    return {
      error: false,
      statusCode: HttpStatus.CREATED,
      msg: 'Reminder created successfully',
      data: resp,
    };
  }

  async updateReminder(
    id: string,
    userId: string,
    updateData: UpdateReminderReqDTO,
  ): Promise<UpdateReminderResDTO> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid reminder ID provided !!');
    }

    const reminderId = new Types.ObjectId(id);

    // Fetch existing reminder with a single query
    const existingReminder = await this.reminderModel
      .findOne({
        _id: reminderId,
        userId,
        isDeleted: false,
      })
      .exec();

    if (!existingReminder) {
      throw new NotFoundException('Reminder not found');
    }

    // Validate and process metadata if provided
    if (updateData.metadata) {
      const metadataDto = this.reminderUtilsService.getMetadataDto(
        existingReminder.categoryString,
        updateData.metadata,
      );

      await this.reminderUtilsService.validateMetadata(metadataDto);

      if (
        existingReminder.categoryString === REMINDER_CATEGORY_ENUM.FOOD &&
        metadataDto instanceof ReminderFoodMetaDataDTO
      ) {
        await this.reminderUtilsService.validateFoodReminderMetadata(
          updateData.metadata as ReminderFoodMetaDataDTO,
          userId,
        );
      }
    }

    // Prepare update payload with type-safe updates
    const updatePayload: UpdateReminderPayloadInterface =
      this.reminderUtilsService.prepareUpdatePayload(updateData);

    // Perform update
    const updatedReminder = await this.reminderModel
      .findByIdAndUpdate(
        id,
        { ...updatePayload, isActive: true },
        {
          new: true,
          runValidators: true,
        },
      )
      .exec();

    await this.notificationMessageModel.updateMany(
      {
        reminderSettingsId: updatedReminder.id,
        isDeleted: false,
      },
      {
        isDeleted: true,
      },
    );

    if (!updatedReminder) {
      throw new InternalServerErrorException('Failed to update reminder');
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Reminder updated successfully',
      data: ReminderDTO.transform(updatedReminder),
    };
  }
}
