import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsObject,
  IsString,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { ReminderSettings } from 'models/reminders';
import { ReminderSoundDTO } from './reminderSound.dto';

export class ReminderDTO {
  @ApiProperty({ description: 'Unique identifier of the reminder' })
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Category of the reminder', example: 'Medicine' })
  @IsString()
  @IsNotEmpty()
  categoryString: string;

  @ApiProperty({
    description: 'Label for the reminder',
    example: 'Take medicine',
  })
  @IsString()
  @IsNotEmpty()
  label: string;

  @ApiProperty({ description: 'Sound associated with the reminder' })
  @IsObject()
  @IsOptional()
  sound: ReminderSoundDTO;

  @ApiProperty({
    description: 'Metadata for additional reminder details',
    example: '{ "repeat": true }',
  })
  @IsObject()
  @IsOptional()
  metadata: Record<string, any>;

  @ApiProperty({ description: 'User identifier who set the reminder' })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'UTC time for the reminder' })
  @IsObject()
  @IsNotEmpty()
  time: Date;

  @ApiProperty({
    description: 'Frontend screen URL for the reminder',
    example: 'https://app.example.com/reminder',
  })
  @IsString()
  @IsOptional()
  frontend_screen_url: string;

  createdAt: Date;

  static transform(reminder: ReminderSettings): ReminderDTO {
    const transformedObj = new ReminderDTO();

    transformedObj.id = reminder._id?.toString();
    transformedObj.userId = reminder.userId.toString();
    transformedObj.label = reminder.label;
    transformedObj.sound = ReminderSoundDTO.transform(reminder.sound);
    transformedObj.categoryString = reminder.categoryString;
    transformedObj.time = reminder.time;
    transformedObj.frontend_screen_url = reminder.frontend_screen_url;
    transformedObj.createdAt = (reminder as any).createdAt;

    if (reminder.metadata && Object.keys(reminder.metadata).length > 0) {
      transformedObj.metadata = reminder.metadata;
    }

    return transformedObj;
  }
}
