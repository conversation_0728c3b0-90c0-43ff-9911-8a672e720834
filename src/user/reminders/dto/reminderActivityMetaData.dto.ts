import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ReminderActivityMetaDataDTO {
  @ApiProperty({ description: 'Name of the exercise', example: 'Running' })
  @IsString()
  @IsNotEmpty()
  exerciseName: string;

  @ApiProperty({
    description: 'Duration of the activity in minutes',
    example: 30,
  })
  @IsNumber()
  @IsNotEmpty()
  activityDuration: number;
}
