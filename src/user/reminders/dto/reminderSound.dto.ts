import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { Reminders_Sound } from 'models/reminders';

export class ReminderSoundDTO {
  @ApiProperty({ example: 'ios_sound.mp3', description: 'Sound file for iOS' })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  ios: string;

  @ApiProperty({
    example: 'android_sound.mp3',
    description: 'Sound file for Android',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  android: string;

  static transform(object: Reminders_Sound): ReminderSoundDTO {
    const transformedObj = new ReminderSoundDTO();

    transformedObj.ios = object.ios;
    transformedObj.android = object.android;

    return transformedObj;
  }
}
