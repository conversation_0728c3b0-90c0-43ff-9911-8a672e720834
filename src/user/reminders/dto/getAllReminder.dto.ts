import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { ReminderDTO } from './reminder.dto';

export class GetAllReminderDTO extends BaseResponse {
  @ApiProperty()
  error: boolean;

  @ApiProperty()
  statusCode: number;

  @ApiProperty({
    description: 'Total number of reminders',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of reminders in the current response',
    example: 10,
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of reminders',
    type: [ReminderDTO],
  })
  data: ReminderDTO[];
}
