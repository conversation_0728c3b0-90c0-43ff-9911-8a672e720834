import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON>rray,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { NUTRITION_MEASUREMENT } from 'models/user-records/Meal-Records';
import { RecipesNutritionByQuantityDTO } from 'src/user/recipe/dto';

export enum QUANTITY_TYPE {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export class ReminderMealsDTO {
  @ApiProperty({
    description: 'The ID of the recipe',
    example: '12345abcde',
  })
  @IsString()
  @IsNotEmpty()
  recipeId: string;

  @ApiProperty({
    enum: NUTRITION_MEASUREMENT,
    description: 'The type of measurement used',
    example: NUTRITION_MEASUREMENT.GRAMS,
  })
  @IsEnum(NUTRITION_MEASUREMENT)
  @IsNotEmpty()
  measurement: NUTRITION_MEASUREMENT;

  @ApiProperty({
    enum: QUANTITY_TYPE,
    description: 'The quantity type',
    example: QUANTITY_TYPE.MEDIUM,
  })
  @IsEnum(QUANTITY_TYPE)
  @IsNotEmpty()
  quantity: QUANTITY_TYPE;

  @ApiProperty({ description: 'Title of the recipe' })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({ description: 'Thumbnail URL of the recipe' })
  @IsString()
  @IsOptional()
  thumbnailUrl: string;

  @ApiProperty({
    description: 'Nutritional breakdown by quantity',
    type: () => RecipesNutritionByQuantityDTO,
  })
  @ValidateNested({ each: true })
  @Type(() => RecipesNutritionByQuantityDTO)
  @IsObject()
  @IsOptional()
  nutritionByQuantity: RecipesNutritionByQuantityDTO;
}

export class ReminderFoodMetaDataDTO {
  @ApiProperty({
    type: [ReminderMealsDTO],
    description: 'Array of meal reminders',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReminderMealsDTO)
  meals: ReminderMealsDTO[];
}
