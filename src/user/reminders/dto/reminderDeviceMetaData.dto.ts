import { IsDate, IsNotEmpty, <PERSON>N<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ReminderDeviceMetaDataDTO {
  @ApiProperty({ description: 'Time usage in minutes', example: 120 })
  @IsNumber()
  @IsNotEmpty()
  timeUsage: number;

  @ApiProperty({
    description: 'Set time for the reminder',
    example: '2025-03-19T14:00:00.000Z',
  })
  @IsDate()
  @IsNotEmpty()
  setTime: Date;
}
