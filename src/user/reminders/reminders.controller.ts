import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Put,
  Param,
  Get,
  Query,
} from '@nestjs/common';
import { RemindersService } from './reminders.service';
import {
  CreateReminderReqDTO,
  CreateReminderResDTO,
  GetAllReminderDTO,
} from './dto';
import { AuthGuard } from 'src/middlewares';
import { Request } from 'express';
import {
  UpdateReminderReqDTO,
  UpdateReminderResDTO,
} from './dto/updateReminder.dto';
import { DeleteReminderDTO } from './dto/deleteReminder.dto';
import { GetAllRemindersQueryInterface } from './interface';
import { GetSingleReminderResDTO } from './dto/getSingleReminder.dto';
import { ApiTags, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';

@ApiTags('Reminders')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('reminders')
export class RemindersController {
  constructor(private readonly remindersService: RemindersService) {}

  @ApiResponse({
    status: 201,
    description: 'Reminder created successfully.',
    type: CreateReminderResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post()
  async CreateReminder(
    @Body() createReminderReqDto: CreateReminderReqDTO,
    @Req() req: Request,
  ): Promise<CreateReminderResDTO> {
    const user = req['user'];
    return this.remindersService.createReminder(createReminderReqDto, user._id);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all reminders.',
    type: GetAllReminderDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async GetAllReminders(
    @Query() queryFilters: GetAllRemindersQueryInterface,
    @Req() req: Request,
  ): Promise<GetAllReminderDTO> {
    const user = req['user'];
    return this.remindersService.getAllReminders(queryFilters, user._id);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the reminder.',
    type: GetSingleReminderResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get(':id')
  async getReminderById(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<GetSingleReminderResDTO> {
    const user = req['user'];
    return this.remindersService.getReminderById(id, user._id);
  }

  @ApiResponse({
    status: 200,
    description: 'Reminder updated successfully.',
    type: UpdateReminderResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put(':id')
  async updateReminder(
    @Param('id') id: string,
    @Body() updateData: UpdateReminderReqDTO,
    @Req() req: Request,
  ): Promise<UpdateReminderResDTO> {
    const user = req['user'];
    return this.remindersService.updateReminder(id, user._id, updateData);
  }

  @ApiResponse({
    status: 200,
    description: 'Reminder deleted successfully.',
    type: DeleteReminderDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('delete/:id')
  async deleteReminder(@Param('id') id: string): Promise<DeleteReminderDTO> {
    return this.remindersService.deleteReminder(id);
  }
}
