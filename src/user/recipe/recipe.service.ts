import {
  Injectable,
  HttpStatus,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Recipes } from 'models/recipe';
import { Model, Types } from 'mongoose';
import {
  CreateRecipeReqDTO,
  CreateRecipeResDTO,
  GetAllRecipeDTO,
  GetSingleRecipeResDTO,
  RecipesDTO,
  UpdateRecipeResDTO,
  UpdateRecipeReqDTO,
  DeleteRecipeDTO,
  GetAllRecipesQueryDTO,
} from './dto';
import { UtilsService } from 'src/common/services';
import { RecipeUtilsService } from './recipe.utils.service';
import { AwsS3Service } from 'src/third-party/aws';
import { DIET_PREFERENCE, User } from 'models/user';
import { Ingredient } from 'models/ingredient';

@Injectable()
export class RecipesService {
  constructor(
    @InjectModel(Recipes.name) private recipeModel: Model<Recipes>,

    @InjectModel(Ingredient.name) private ingredientModel: Model<Ingredient>,

    private readonly utilsService: UtilsService,

    private readonly createRecipeUtilsService: RecipeUtilsService,

    private readonly s3Service: AwsS3Service,
  ) {}

  async createRecipe(
    recipeData: CreateRecipeReqDTO,
    thumbnailFile: Express.Multer.File,
    userId: string,
  ): Promise<CreateRecipeResDTO> {
    const parsedNutritions =
      this.createRecipeUtilsService.parseAndValidateNutritionData(
        recipeData.nutritionByQuantity,
      );

    this.createRecipeUtilsService.validateUniqueQuantities(parsedNutritions);

    const createData = this.createRecipeUtilsService.buildRecipeData(
      recipeData,
      parsedNutritions,
    );

    if (thumbnailFile) {
      createData.thumbnailUrl =
        await this.createRecipeUtilsService.uploadThumbnail(thumbnailFile);
    }

    createData.author = userId;

    const newRecipe = await this.recipeModel.create(createData);
    const resp = RecipesDTO.transform(newRecipe);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Recipe created successfully !!',
      data: resp,
    };
  }

  async getAllRecipes(
    queryFilters: GetAllRecipesQueryDTO,
    user: User,
  ): Promise<GetAllRecipeDTO> {
    const { page, title, mealType } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const userId = user._id;

    // If myrecipe is present, only return the user's own recipes
    if ('myrecipe' in queryFilters) {
      const query = {
        isDeleted: false,
        author: userId,
      };

      const recipes = await this.recipeModel
        .find(query)
        .skip(offset)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec();

      const total = await this.recipeModel.countDocuments(query);

      const recipeResponses = recipes.map((item) => RecipesDTO.transform(item));

      return {
        error: false,
        statusCode: HttpStatus.OK,
        total,
        nbHits: recipeResponses.length,
        data: recipeResponses,
      };
    }

    // Default logic when myrecipe is not passed
    const query: any = {
      isDeleted: false,
      $or: [{ author: userId }, { author: null }],
      isPublished: true,
    };

    if (title && title.trim().length > 0) {
      query.title = { $regex: new RegExp(title.trim(), 'i') };
    }

    if (mealType) {
      query.mealType = { $regex: new RegExp(mealType, 'i') };
    }

    if (user.diet_preference) {
      if (user.diet_preference === DIET_PREFERENCE.VEG) {
        query.$and = [
          {
            $or: [
              { author: userId },
              {
                author: null,
                category: { $in: [DIET_PREFERENCE.VEG, DIET_PREFERENCE.VEGAN] },
              },
            ],
          },
        ];
      } else if (user.diet_preference === DIET_PREFERENCE.NON_VEG) {
        query.$and = [
          {
            $or: [
              { author: userId },
              {
                author: null,
                category: {
                  $in: [
                    DIET_PREFERENCE.VEG,
                    DIET_PREFERENCE.NON_VEG,
                    DIET_PREFERENCE.VEGAN,
                  ],
                },
              },
            ],
          },
        ];
      } else if (user.diet_preference === DIET_PREFERENCE.VEGAN) {
        query.$or = [
          { author: userId },
          { author: null, category: DIET_PREFERENCE.VEGAN },
        ];
      } else if (user.diet_preference === DIET_PREFERENCE.OTHERS) {
        query.$or = [
          { author: userId },
          { author: null, category: DIET_PREFERENCE.OTHERS },
        ];
      }
    }

    const recipes = await this.recipeModel
      .find(query)
      .skip(offset)
      .limit(limit)
      .sort({ createdAt: -1 })
      .exec();

    const total = await this.recipeModel.countDocuments(query);

    const recipeResponses = recipes.map((item) => RecipesDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: recipeResponses.length,
      data: recipeResponses,
    };
  }

  async getRecipeById(
    id: string,
    userId: string,
  ): Promise<GetSingleRecipeResDTO> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid recipe ID');
    }

    const recipe = await this.recipeModel.findOne({
      _id: id,
      isDeleted: false,
      $or: [{ author: userId }, { author: null }],
    });

    if (!recipe) {
      throw new NotFoundException('Recipe not found !!');
    }

    // Check if ingredients are ObjectIds (i.e., reference-based recipe)
    const firstIngredient = recipe.ingredients?.[0];
    let ingredientNames: string[] = [];

    if (firstIngredient && Types.ObjectId.isValid(firstIngredient)) {
      // Fetch all ingredient documents
      const ingredientDocs = await this.ingredientModel.find({
        _id: { $in: recipe.ingredients },
        isDeleted: false,
      });

      // Check if any ingredient IDs were not found
      const foundIds = ingredientDocs.map((doc) => doc._id.toString());
      const missingIds = recipe.ingredients
        .map((id) => id.toString())
        .filter((id) => !foundIds.includes(id));

      if (missingIds.length > 0) {
        throw new NotFoundException(
          `Ingredients not found for IDs: ${missingIds.join(', ')}`,
        );
      }

      // Map to names
      ingredientNames = ingredientDocs.map((ingredient) => ingredient.name);
    } else {
      // Already in name format
      ingredientNames = recipe.ingredients.map((ing) => ing.toString());
    }

    // Transform recipe using DTO
    const transformedRecipe = RecipesDTO.transform(recipe);

    // Override the ingredients with names
    transformedRecipe.ingredients = ingredientNames;

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: transformedRecipe,
    };
  }

  async updateRecipe(
    id: string,
    updateBody: UpdateRecipeReqDTO,
    thumbnailFile: Express.Multer.File,
    userId: string,
  ): Promise<UpdateRecipeResDTO> {
    const { title, ingredients, directions, timeToPrep, nutritionByQuantity } =
      updateBody;

    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid recipe ID');
    }

    const recipe = await this.recipeModel.findOne({ _id: id, author: userId });

    if (!recipe || recipe.isDeleted) {
      throw new NotFoundException('Recipe not found');
    }

    const updateData: any = {};

    if (title) updateData.title = title;

    if (ingredients) updateData.ingredients = ingredients.split(',');

    if (directions) updateData.directions = directions;

    if (timeToPrep) updateData.timeToPrep = timeToPrep;

    if (nutritionByQuantity) {
      const parsedNutritions =
        this.createRecipeUtilsService.parseAndValidateNutritionData(
          nutritionByQuantity,
        );
      this.createRecipeUtilsService.validateUniqueQuantities(parsedNutritions);

      updateData.nutritionByQuantity = parsedNutritions;
    }

    if (thumbnailFile) {
      updateData.thumbnailUrl = (
        await this.s3Service.uploadFile(thumbnailFile)
      ).Location;
    }

    const updatedRecipe = await this.recipeModel
      .findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      })
      .exec();

    const resp = RecipesDTO.transform(updatedRecipe);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Updated Recipe Successfully !!',
      data: resp,
    };
  }

  async deleteRecipe(id: string, user: User): Promise<DeleteRecipeDTO> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid recipe ID');
    }

    const recipe = await this.recipeModel
      .findOne({ _id: id, isDeleted: false, author: user._id })
      .exec();

    if (!recipe) {
      throw new NotFoundException('Recipe not found !!');
    }

    // Soft delete by updating isDeleted field
    recipe.isDeleted = true;
    await recipe.save();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      message: 'Recipe deleted successfully',
    };
  }
}
