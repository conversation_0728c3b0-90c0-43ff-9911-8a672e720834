import { Modu<PERSON> } from '@nestjs/common';
import { RecipesService } from './recipe.service';
import { RecipesController } from './recipe.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Recipes, RecipesSchema } from 'models/recipe';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';
import { CommonModule } from 'src/common/common.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { RecipeUtilsService } from './recipe.utils.service';
import { Ingredient } from 'models/ingredient';
import { IngredientSchema } from 'models/ingredient/ingredient.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Recipes.name, schema: RecipesSchema },
      { name: Ingredient.name, schema: IngredientSchema },
    ]),
    AuthModule,
    RepoModule,
    CommonModule,
    ThirdPartyModule,
  ],
  controllers: [RecipesController],
  providers: [RecipesService, RecipeUtilsService],
  exports: [MongooseModule],
})
export class RecipeModule {}
