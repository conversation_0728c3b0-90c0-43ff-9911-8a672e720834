import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Query,
  Param,
  Put,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { RecipesService } from './recipe.service';
import {
  CreateRecipeReqDTO,
  CreateRecipeResDTO,
  DeleteRecipeDTO,
  GetAllRecipeDTO,
  GetAllRecipesQueryDTO,
  GetSingleRecipeResDTO,
  UpdateRecipeReqDTO,
  UpdateRecipeResDTO,
} from './dto';
import { AuthGuard } from 'src/middlewares';
import { Request } from 'express';
import {
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
  ApiConsumes,
} from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  AllowedImageExtensions,
  getMulterMediaOptions,
} from 'src/utils/multer';

@ApiTags('Recipes')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('recipes')
export class RecipesController {
  constructor(private readonly recipesService: RecipesService) {}

  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: 'Recipe created successfully',
    type: CreateRecipeResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @Post()
  async createRecipe(
    @Body() recipeData: CreateRecipeReqDTO,
    @UploadedFile() thumbnailFile: Express.Multer.File,
    @Req() req: Request,
  ): Promise<CreateRecipeResDTO> {
    const user = req['user'];

    return this.recipesService.createRecipe(
      recipeData,
      thumbnailFile,
      user._id,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all recipes.',
    type: GetAllRecipeDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async getAllRecipes(
    @Req() req: Request,
    @Query() queryFilters: GetAllRecipesQueryDTO,
  ): Promise<GetAllRecipeDTO> {
    const user = req['user'];
    return this.recipesService.getAllRecipes(queryFilters, user);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the recipe.',
    type: GetSingleRecipeResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get(':id')
  async getRecipeById(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<GetSingleRecipeResDTO> {
    const user = req['user'];
    return this.recipesService.getRecipeById(id, user._id);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully updated the Recipe .',
    type: UpdateRecipeResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @Put('/:id')
  async updateRecipe(
    @Param('id') id: string,
    @Body() updateBody: UpdateRecipeReqDTO,
    @Req() req: Request,
    @UploadedFile() thumbnailFile: Express.Multer.File,
  ) {
    const user = req['user'];
    return this.recipesService.updateRecipe(
      id,
      updateBody,
      thumbnailFile,
      user._id,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Recipe deleted successfully',
    type: DeleteRecipeDTO,
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
  })
  @Put('delete/:id')
  async deleteRecipe(
    @Req() req: Request,
    @Param('id') id: string,
  ): Promise<DeleteRecipeDTO> {
    const user = req['user'];

    return this.recipesService.deleteRecipe(id, user);
  }
}
