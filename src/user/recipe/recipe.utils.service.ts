import { BadRequestException, Injectable } from '@nestjs/common';
import { RecipesNutritionByQuantityDTO } from './dto';
import { CustomLogger } from 'src/common/services';
import { SERVING_NUTRITION_QUANTITY } from 'models/recipe';
import { validateSync } from 'class-validator';
import { AwsS3Service } from 'src/third-party/aws';

@Injectable()
export class RecipeUtilsService {
  constructor(
    private readonly logger: CustomLogger,
    private readonly s3Service: AwsS3Service,
  ) {}

  parseAndValidateNutritionData(
    nutritionData: string,
  ): RecipesNutritionByQuantityDTO[] {
    let parsedNutritions: RecipesNutritionByQuantityDTO[] = [];
    let parsedData: any;
    try {
      parsedData = JSON.parse(nutritionData);

      if (!Array.isArray(parsedData)) {
        throw new Error();
      }
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Unable to parse nutrition data...');
    }

    parsedNutritions = parsedData.map((item) => {
      const newData = new RecipesNutritionByQuantityDTO();
      newData.quantity =
        SERVING_NUTRITION_QUANTITY[
          item.quantity.toUpperCase() as keyof typeof SERVING_NUTRITION_QUANTITY
        ];
      newData.protein = item.protein;
      newData.fats = item.fats;
      newData.calories = item.calories;
      newData.fiber = item.fiber;
      newData.carbs = item.carbs;
      return newData;
    });

    for (const item of parsedNutritions) {
      const errors = validateSync(item);
      if (errors.length > 0) {
        throw new BadRequestException(
          `Invalid Nutrition data : ${Object.values(errors[0].constraints).reverse()[0]}`,
        );
      }
    }

    return parsedNutritions;
  }

  validateUniqueQuantities(
    parsedNutritions: RecipesNutritionByQuantityDTO[],
  ): void {
    const uniqueQuantities = new Set<string>();

    for (const item of parsedNutritions) {
      if (uniqueQuantities.has(item.quantity)) {
        throw new BadRequestException(
          `Duplicate quantity found: ${item.quantity}`,
        );
      }

      uniqueQuantities.add(item.quantity);
    }
  }

  buildRecipeData(
    RecipeData: any,
    parsedNutritions: RecipesNutritionByQuantityDTO[],
  ): any {
    return {
      title: RecipeData.title,
      ingredients: Array.isArray(RecipeData.ingredients)
        ? RecipeData.ingredients
        : RecipeData.ingredients.split(','),
      directions: RecipeData.directions,
      timeToPrep: RecipeData.timeToPrep,
      mealType: RecipeData.mealType,
      nutritionByQuantity: parsedNutritions,
      isPublished: RecipeData.isPublished,
      thumbnailUrl: RecipeData.thumbnailUrl,
      category: RecipeData.category,
    };
  }

  async uploadThumbnail(thumbnailFile: Express.Multer.File): Promise<string> {
    return (await this.s3Service.uploadFile(thumbnailFile)).Location;
  }
}
