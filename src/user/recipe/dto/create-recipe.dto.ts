import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

import { RecipesNutritionByQuantityDTO } from './nutritionByQuantity.dto';
import { BaseResponse } from 'src/utils/responses';
import { RecipesDTO } from './recipe.dto';

export class CreateRecipeReqDTO {
  @ApiProperty({
    description: 'Title of the recipe',
    example: 'Grilled Chicken Salad',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'List of ingredients',
    example: ['Chicken', 'Lettuce', 'Olive Oil'],
  })
  @IsString()
  @IsNotEmpty()
  ingredients: string;

  @ApiProperty({
    description: 'Nutritional information by quantity',
    type: [RecipesNutritionByQuantityDTO],
  })
  @IsString()
  @IsNotEmpty()
  nutritionByQuantity: string;
}

export class CreateRecipeResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Recipe created successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Created recipe details', type: RecipesDTO })
  data: RecipesDTO;
}
