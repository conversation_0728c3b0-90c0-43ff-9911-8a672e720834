import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, <PERSON>N<PERSON><PERSON> } from 'class-validator';
import {
  PIECE_NUTRITION_QUANTITY,
  RecipesNutritionByQuantity,
  SERVING_NUTRITION_QUANTITY,
  SLICE_NUTRITION_QUANTITY,
} from 'models/recipe';

export class RecipesNutritionByQuantityDTO {
  @ApiProperty({
    description: 'Quantity size',
    enum: [
      ...Object.values(SERVING_NUTRITION_QUANTITY),
      ...Object.values(SLICE_NUTRITION_QUANTITY),
      ...Object.values(PIECE_NUTRITION_QUANTITY),
    ],
    example: SERVING_NUTRITION_QUANTITY.MEDIUM,
  })
  @IsEnum([
    ...Object.values(SERVING_NUTRITION_QUANTITY),
    ...Object.values(SLICE_NUTRITION_QUANTITY),
    ...Object.values(PIECE_NUTRITION_QUANTITY),
  ])
  @IsNotEmpty()
  quantity:
    | SERVING_NUTRITION_QUANTITY
    | SLICE_NUTRITION_QUANTITY
    | PIECE_NUTRITION_QUANTITY;

  @ApiProperty({ description: 'Amount of protein in grams', example: 25 })
  @IsNumber()
  @IsNotEmpty()
  protein: number;

  @ApiProperty({ description: 'Amount of calories in kcal', example: 25 })
  @IsNumber()
  @IsNotEmpty()
  calories: number;

  @ApiProperty({ description: 'Amount of fats in grams', example: 10 })
  @IsNumber()
  @IsNotEmpty()
  fats: number;

  @ApiProperty({ description: 'Amount of fiber in grams', example: 5 })
  @IsNumber()
  @IsNotEmpty()
  fiber: number;

  @ApiProperty({ description: 'Amount of carbohydrates in grams', example: 50 })
  @IsNumber()
  @IsNotEmpty()
  carbs: number;

  static transform(
    object: RecipesNutritionByQuantity,
  ): RecipesNutritionByQuantityDTO {
    const transformedObj = new RecipesNutritionByQuantityDTO();
    if (!object) return transformedObj;
    transformedObj.quantity = object.quantity;
    transformedObj.protein = object.protein;
    transformedObj.calories = object.calories;
    transformedObj.fats = object.fats;
    transformedObj.fiber = object.fiber;
    transformedObj.carbs = object.carbs;
    return transformedObj;
  }
}
