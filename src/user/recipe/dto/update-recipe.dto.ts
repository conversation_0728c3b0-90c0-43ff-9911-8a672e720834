import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { BaseResponse } from 'src/utils/responses';
import { RecipesDTO } from './recipe.dto';

export class UpdateRecipeReqDTO {
  @ApiPropertyOptional({
    description: 'Title of the recipe',
    example: 'Spaghetti Bolognese',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Ingredients list',
    example: 'Tomatoes, Ground Beef, Pasta',
  })
  @IsOptional()
  @IsString()
  ingredients?: string;

  @ApiPropertyOptional({
    description: 'Cooking directions',
    example: 'Boil pasta, cook beef, mix with sauce',
  })
  @IsString()
  @IsOptional()
  directions?: string;

  @ApiPropertyOptional({
    description: 'Time to prepare the dish (in minutes)',
    example: 30,
  })
  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  timeToPrep?: number;

  @ApiPropertyOptional({ description: 'Nutritional information by quantity' })
  @IsString()
  @IsOptional()
  nutritionByQuantity: string;
}

export class UpdateRecipeResDTO extends BaseResponse {
  @ApiProperty()
  msg: string;

  @ApiProperty({
    description: 'Updated recipe details',
    type: () => RecipesDTO,
  })
  data: RecipesDTO;
}
