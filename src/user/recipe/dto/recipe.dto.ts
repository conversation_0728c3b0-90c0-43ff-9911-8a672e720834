import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
  IsArray,
  ValidateNested,
  IsMongoId,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MEAL_TYPES, Recipes } from 'models/recipe';
import { DIET_PREFERENCE } from 'models/user';
import { RecipesNutritionByQuantityDTO } from './nutritionByQuantity.dto';
import { Types } from 'mongoose';

export class RecipesDTO {
  @ApiProperty({ description: 'Unique identifier of the recipe' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Title of the recipe' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'List of ingredients' })
  @IsArray()
  @IsNotEmpty()
  ingredients: string[];

  @ApiProperty({ description: 'Cooking directions' })
  @IsString()
  @IsNotEmpty()
  directions: string;

  @ApiProperty({ description: 'Time required for preparation' })
  @Min(10)
  @IsNumber()
  @IsNotEmpty()
  timeToPrep: number;

  @ApiProperty({ description: 'Thumbnail URL of the recipe' })
  @IsString()
  thumbnailUrl: string;

  @ApiProperty({ description: 'Meal type (e.g., breakfast, lunch, dinner)' })
  @IsEnum(MEAL_TYPES)
  @IsNotEmpty()
  mealType: MEAL_TYPES;

  @ApiProperty({
    description: 'Nutritional breakdown by quantity',
    type: () => [RecipesNutritionByQuantityDTO],
  })
  @ValidateNested({ each: true })
  @Type(() => RecipesNutritionByQuantityDTO)
  @IsArray()
  @IsNotEmpty()
  nutritionByQuantity: RecipesNutritionByQuantityDTO[];

  @ApiProperty({
    description: 'Author ID (null for admin recipes, user ID for user recipes)',
  })
  @IsMongoId()
  author: Types.ObjectId;

  @ApiProperty({
    description: 'category of dish',
    enum: DIET_PREFERENCE,
    example: DIET_PREFERENCE.VEG,
  })
  @IsEnum(DIET_PREFERENCE)
  category: DIET_PREFERENCE;

  createdAt: Date;

  static transform(object: Recipes): RecipesDTO {
    const transformedObj = new RecipesDTO();
    transformedObj.id = object._id.toString();
    transformedObj.title = object.title;
    transformedObj.ingredients = object.ingredients.map((ing) =>
      ing.toString(),
    );
    transformedObj.directions = object.directions;
    transformedObj.timeToPrep = object.timeToPrep;
    transformedObj.thumbnailUrl = object.thumbnailUrl;
    transformedObj.mealType = object.mealType as MEAL_TYPES;
    transformedObj.nutritionByQuantity = object.nutritionByQuantity.map(
      RecipesNutritionByQuantityDTO.transform,
    );

    transformedObj.category = object.category as DIET_PREFERENCE;
    transformedObj.author = object.author;
    transformedObj.createdAt = (object as any).createdAt;
    return transformedObj;
  }
}
