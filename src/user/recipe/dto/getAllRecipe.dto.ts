import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { RecipesDTO } from './recipe.dto';

export class GetAllRecipeDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of recipes',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of recipes in the current response',
    example: 10,
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of recipes',
    type: [RecipesDTO],
  })
  data: RecipesDTO[];
}
