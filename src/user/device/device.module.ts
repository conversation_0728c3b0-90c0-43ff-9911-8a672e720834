import { Module } from '@nestjs/common';
import { UserDeviceService } from './device.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Device,
  DeviceSchema,
  UserDeviceConnections,
  UserDeviceConnectionsSchema,
} from 'models/device';
import { UserDeviceController } from './device.controller';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Device.name, schema: DeviceSchema },
      { name: UserDeviceConnections.name, schema: UserDeviceConnectionsSchema },
    ]),
    AuthModule,
    CommonModule,
    RepoModule,
  ],
  providers: [UserDeviceService],
  exports: [UserDeviceService],
  controllers: [UserDeviceController],
})
export class UserDeviceModule {}
