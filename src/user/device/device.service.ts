import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CreateDeviceConnectionReqDTO,
  CreateDeviceConnectionResDTO,
  DeviceUserDTO,
  GetAllDevicesToUserResDTO,
  RemoveDeviceConnectionReqDTO,
  UserDeviceConnectionsDTO,
} from './device-dto';
import {
  Device,
  DEVICE_CONNECTION_STATUS,
  UserDeviceConnections,
} from 'models/device';
import { User } from 'models/user';
import { UtilsService } from 'src/common/services';
import { GetConnectedDeviceResDTO } from './device-dto/getConnectedDeviceRes.dto';
import { getAllDevicesToUserQueryInterface } from './interfaces';

@Injectable()
export class UserDeviceService {
  constructor(
    private readonly utilsService: UtilsService,
    @InjectModel(UserDeviceConnections.name)
    private readonly userDeviceConnectionModel: Model<UserDeviceConnections>,

    @InjectModel(Device.name)
    private readonly deviceModel: Model<Device>,
  ) {}

  async createDeviceConnection(
    user: User,
    createDeviceData: CreateDeviceConnectionReqDTO,
  ): Promise<CreateDeviceConnectionResDTO> {
    try {
      const { deviceId, serialId } = createDeviceData;

      const existingDevice = await this.deviceModel.findById(deviceId);
      if (!existingDevice) {
        throw new BadRequestException(`Device not found with id: ${deviceId}`);
      }

      if (!existingDevice.serialIds.includes(serialId)) {
        throw new BadRequestException('Invalid Serial Id provided.');
      }

      // Check if the user already has this device connected
      const existingConnection = await this.userDeviceConnectionModel.findOne({
        userId: user._id.toString(),
        deviceId,
        serialId,
        connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED, // Only check for active connections
      });

      if (existingConnection) {
        throw new BadRequestException('Device is already connected.');
      }

      // Disconnect all existing connections for the user
      await this.userDeviceConnectionModel.updateMany(
        { userId: user._id.toString() },
        { $set: { connectionStatus: DEVICE_CONNECTION_STATUS.DISCONNECTED } },
      );

      // Upsert a new connection
      const updatedConnection =
        await this.userDeviceConnectionModel.findOneAndUpdate(
          { userId: user._id.toString(), deviceId, serialId },
          { connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED },
          { new: true, upsert: true },
        );

      return {
        msg: 'Device Connected Successfully!',
        deviceConnectionData:
          UserDeviceConnectionsDTO.transform(updatedConnection),
      };
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Failed to connect device',
      );
    }
  }

  async getAllDevicesToUser(
    filterQueries: getAllDevicesToUserQueryInterface,
  ): Promise<GetAllDevicesToUserResDTO> {
    const { page } = filterQueries;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 20);

    const devices = await this.deviceModel
      .find({ isDeleted: false })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset);

    const total = await this.deviceModel.countDocuments();

    const resp = devices.map((item) => DeviceUserDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: resp.length,
      devices: resp,
    };
  }

  async getConnectedDevice(userId: string): Promise<GetConnectedDeviceResDTO> {
    // Fetch the connected device for the user
    const userDeviceConnection = await this.userDeviceConnectionModel.findOne({
      userId,
      connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED,
    });

    if (!userDeviceConnection) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        device: null,

        connectionStatus: DEVICE_CONNECTION_STATUS.DISCONNECTED,
      };
    }

    // Fetch device details
    const device = await this.deviceModel.findOne({
      _id: userDeviceConnection.deviceId,
      isDeleted: false,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      device: device ? DeviceUserDTO.transform(device) : null,
      serialId: userDeviceConnection.serialId, // Add serialId to response
      connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED,
    };
  }

  async createNewDeviceConnection(
    user: User,
    createDeviceData: CreateDeviceConnectionReqDTO,
  ) {
    try {
      const { deviceId, serialId } = createDeviceData;

      if (!deviceId || !serialId) {
        throw new BadRequestException({
          statusCode: HttpStatus.BAD_REQUEST,
          success: false,
          message: 'Device ID and Serial ID are required!',
        });
      }

      const response = await this.createDeviceConnection(
        user,
        createDeviceData,
      );
      return {
        statusCode: HttpStatus.OK,
        success: true,
        message: response.msg,
        data: response.deviceConnectionData,
      };
    } catch (error) {
      throw new BadRequestException({
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message || 'Failed to connect device',
      });
    }
  }

  async removeDeviceConnection(
    userId: string,
    removeDeviceData: RemoveDeviceConnectionReqDTO,
  ) {
    try {
      const { deviceId } = removeDeviceData;

      // Find the user's device connection
      const existingConnection = await this.userDeviceConnectionModel.findOne({
        userId,
        deviceId,
      });

      if (!existingConnection) {
        throw new BadRequestException({
          statusCode: HttpStatus.NOT_FOUND,
          success: false,
          message: 'Device connection not found!',
        });
      }

      // Remove the device connection
      await this.userDeviceConnectionModel.deleteOne({ userId, deviceId });

      return {
        statusCode: HttpStatus.OK,
        success: true,
        message: 'Device disconnected successfully!',
      };
    } catch (error) {
      throw new BadRequestException({
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message || 'Failed to remove device connection',
      });
    }
  }
}
