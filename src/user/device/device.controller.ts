import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
// import { DeviceService } from './device.service';
import { UserDeviceService } from './device.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { AuthGuard } from 'src/middlewares';
import {
  CreateDeviceConnectionReqDTO,
  RemoveDeviceConnectionReqDTO,
} from './device-dto';
import { GetConnectedDeviceResDTO } from './device-dto/getConnectedDeviceRes.dto';
import { getAllDevicesToUserQueryInterface } from './interfaces';

@ApiTags('User-Devices')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('connected-device')
export class UserDeviceController {
  constructor(private readonly userdeviceService: UserDeviceService) {}

  @ApiOperation({ summary: 'Retrieve all devices associated with the user' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the devices.',
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async GetAllUserDevices(
    @Query() filterQueries: getAllDevicesToUserQueryInterface,
  ) {
    return this.userdeviceService.getAllDevicesToUser(filterQueries);
  }

  @ApiOperation({ summary: 'Retrieve the connected device of the user' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the connected device.',
    type: GetConnectedDeviceResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'No connected device found.',
    type: ErrorResponse,
  })
  @Get()
  async GetConnectedDevice(@Request() req): Promise<GetConnectedDeviceResDTO> {
    return this.userdeviceService.getConnectedDevice(req.user._id);
  }

  @ApiOperation({ summary: 'Connect a new device to the user' })
  @ApiResponse({
    status: 201,
    description: 'Device connected successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @Post()
  async connectDevice(
    @Req() req: Request,
    @Body() createDeviceData: CreateDeviceConnectionReqDTO,
  ) {
    return this.userdeviceService.createNewDeviceConnection(
      req['user'],
      createDeviceData,
    );
  }

  @ApiOperation({ summary: 'Remove an existing device connection' })
  @ApiResponse({
    status: 200,
    description: 'Device connection removed successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 404,
    description: 'Device connection not found.',
    type: ErrorResponse,
  })
  @Put()
  async removeDeviceConnection(
    @Request() req,
    @Body() removeDeviceData: RemoveDeviceConnectionReqDTO,
  ) {
    return this.userdeviceService.removeDeviceConnection(
      req['user']._id,
      removeDeviceData,
    );
  }
}
