import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Device } from 'models/device';
import { UtilsService } from 'src/common/services';
import { getAllDevicesToUserQueryInterface } from '../interfaces';
import { DeviceUserDTO, GetAllDevicesToUserResDTO } from '../device-dto';

@Injectable()
export class DeviceService {
  constructor(
    private readonly utilsService: UtilsService,
    @InjectModel(Device.name)
    private readonly deviceModel: Model<Device>,
  ) {}

  async getAllDevicesToUser(
    filterQueries: getAllDevicesToUserQueryInterface,
  ): Promise<GetAllDevicesToUserResDTO> {
    const { page } = filterQueries;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 6);

    const devices = await this.deviceModel
      .find({ isDeleted: false })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset);

    const total = await this.deviceModel.countDocuments({ isDeleted: false });

    const resp = devices.map((item) => DeviceUserDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: resp.length,
      devices: resp,
    };
  }
}
