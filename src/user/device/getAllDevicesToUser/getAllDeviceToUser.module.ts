import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DeviceController } from './getAllDevicesToUSer.controller';
import { DeviceService } from './getAllDeviceToUser.service';
import { Device, DeviceSchema } from 'models/device';
import { UtilsService } from 'src/common/services';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Device.name, schema: DeviceSchema }]),
  ],
  controllers: [DeviceController],
  providers: [DeviceService, UtilsService],
  exports: [DeviceService],
})
export class DeviceModule {}
