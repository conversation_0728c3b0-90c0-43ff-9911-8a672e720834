import { Controller, Get, Query } from '@nestjs/common';
import { DeviceService } from './getAllDeviceToUser.service';
import { GetAllDevicesToUserResDTO } from '../device-dto';
import { getAllDevicesToUserQueryInterface } from '../interfaces';

@Controller('allDeviceToUser')
export class DeviceController {
  constructor(private readonly deviceService: DeviceService) {}

  @Get()
  async getAllDevicesToUser(
    @Query() filterQueries: getAllDevicesToUserQueryInterface,
  ): Promise<GetAllDevicesToUserResDTO> {
    return this.deviceService.getAllDevicesToUser(filterQueries);
  }
}
