import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsString, IsNotEmpty } from 'class-validator';

export class CreateDeviceConnectionReqDTO {
  @ApiProperty({
    description: 'The unique identifier for the device',
    example: '60d21b4667d0d8992e610c85',
  })
  @IsMongoId({ message: 'Invalid MongoDB ObjectId format for deviceId' }) // Ensures it's a valid ObjectId
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({
    description: 'The serial ID of the device',
    example: 'ABCD1234',
  })
  @IsString()
  @IsNotEmpty()
  serialId: string;
}
