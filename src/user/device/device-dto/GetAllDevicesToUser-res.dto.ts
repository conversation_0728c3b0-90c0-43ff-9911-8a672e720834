import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { DeviceUserDTO } from './userDevice.dto';

export class GetAllDevicesToUserResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of devices available',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of devices returned in this response',
    example: 10,
  })
  nbHits: number;

  @ApiProperty({ type: [DeviceUserDTO], description: 'List of devices' })
  devices: DeviceUserDTO[];
}
