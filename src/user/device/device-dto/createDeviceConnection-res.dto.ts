import { ApiProperty } from '@nestjs/swagger';
import { UserDeviceConnectionsDTO } from './userDeviceConnection.dto';

export class CreateDeviceConnectionResDTO {
  @ApiProperty({
    description: 'Message indicating the status of the device creation process',
    example: 'Device connected successfully!',
  })
  msg: string;

  @ApiProperty({
    description: 'The device connection data that was created',
    type: UserDeviceConnectionsDTO,
  })
  deviceConnectionData: UserDeviceConnectionsDTO;
}
