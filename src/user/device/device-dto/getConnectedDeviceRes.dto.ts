import { ApiProperty } from '@nestjs/swagger';
import { DEVICE_CONNECTION_STATUS } from 'models/device';
import { DeviceUserDTO } from './userDevice.dto';

export class GetConnectedDeviceResDTO {
  @ApiProperty()
  error: boolean;

  @ApiProperty()
  statusCode: number;

  @ApiProperty({ type: DeviceUserDTO, nullable: true })
  device: DeviceUserDTO | null;

  @ApiProperty()
  serialId?: string;

  @ApiProperty({ enum: DEVICE_CONNECTION_STATUS })
  connectionStatus: DEVICE_CONNECTION_STATUS;
}
