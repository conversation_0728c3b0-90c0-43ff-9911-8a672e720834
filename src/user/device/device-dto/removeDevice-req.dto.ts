import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsString, IsNotEmpty } from 'class-validator';

export class RemoveDeviceConnectionReqDTO {
  @ApiProperty({
    description: 'The unique identifier for the device',
    example: '60d21b4667d0d8992e610c85',
  })
  @IsMongoId({ message: 'Invalid MongoDB ObjectId format for deviceId' })
  @IsString()
  @IsNotEmpty()
  deviceId: string;
}
