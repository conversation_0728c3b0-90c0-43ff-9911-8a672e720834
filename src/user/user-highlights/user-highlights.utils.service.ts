import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { HttpStatus } from '@nestjs/common';
import { UserSleepRecords } from 'models/user-records/Sleep-Records';
import * as moment from 'moment';
import { OpenAIService } from 'src/third-party/openAI/openai.service';
import {
  UserWeightRecords,
  WeightHighlight,
} from 'models/user-records/Weight-Records';
import { GOAL_TYPES, User } from 'models/user';
import {
  DailyMoodAverage,
  MoodHighlight,
} from 'models/user-records/Mood-Records';
import { SleepHighlight } from 'models/user-records/Sleep-Records/userSleepHighLight.schema';
import {
  MealHighlight,
  UserMealRecords,
} from 'models/user-records/Meal-Records';
import {
  DeviceUsageHighlight,
  UserDeviceRecord,
} from 'models/user-records/Device-records';

@Injectable()
export class HighLightsUtilsService {
  constructor(
    @InjectModel(UserSleepRecords.name)
    private readonly sleepRecordModel: Model<UserSleepRecords>,
    @InjectModel(SleepHighlight.name)
    private readonly sleepHighlightModel: Model<SleepHighlight>,
    @InjectModel(UserWeightRecords.name)
    private readonly userWeightRecordModel: Model<UserWeightRecords>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    @InjectModel(DailyMoodAverage.name)
    private readonly dailyMoodAverageModel: Model<DailyMoodAverage>,
    @InjectModel(MoodHighlight.name)
    private readonly moodHighlightModel: Model<MoodHighlight>,
    @InjectModel(WeightHighlight.name)
    private readonly weightHighlightModel: Model<WeightHighlight>,
    @InjectModel(UserMealRecords.name)
    private readonly userMealRecordModel: Model<UserMealRecords>,
    @InjectModel(MealHighlight.name)
    private readonly mealHighlightModel: Model<MealHighlight>,
    @InjectModel(DeviceUsageHighlight.name)
    private readonly deviceUsageHighlightModel: Model<DeviceUsageHighlight>,
    @InjectModel(UserDeviceRecord.name)
    private readonly userDeviceRecordModel: Model<UserDeviceRecord>,
    private readonly openAIService: OpenAIService,
  ) {}

  async getSleepHighlight(userId: string) {
    const today = moment().endOf('day');
    const sevenDaysAgo = moment().subtract(6, 'days').startOf('day');

    // Fetch user
    const user = await this.userModel.findById(userId).lean();
    if (!user) {
      return {
        error: true,
        statusCode: HttpStatus.NOT_FOUND,
        message: 'User not found',
      };
    }

    // Get user's sleep goal
    const sleepGoalObj = user.goals?.find(
      (goal: any) => goal.goal_type === GOAL_TYPES.SLEEP,
    );
    const sleepGoalText =
      sleepGoalObj?.selected_goal || 'No specific goal mentioned';

    // Fetch the last 7 days of sleep records
    const sleepRecords = await this.sleepRecordModel
      .find({
        userId,
        date: { $gte: sevenDaysAgo.toDate(), $lte: today.toDate() },
      })
      .sort({ date: 1 });

    if (sleepRecords.length === 0) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: [],
      };
    }

    const latestRecord = sleepRecords[sleepRecords.length - 1];

    // Check if highlight already exists
    const existingHighlight = await this.sleepHighlightModel.findOne({
      userId,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    if (existingHighlight) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: existingHighlight.highlight_text,
      };
    }

    // Format summary string
    const sleepSummary = sleepRecords
      .map(
        (record) =>
          `Date: ${moment(record.date).format('YYYY-MM-DD')}, Hours: ${record.numOfHours || 'N/A'}`,
      )
      .join('; ');

    // Build prompt
    const basePrompt = process.env.SLEEP_FEEDBACK_PROMPT;
    if (
      !basePrompt?.includes('{sleepGoal}') ||
      !basePrompt.includes('{sleepRecords}')
    ) {
      throw new InternalServerErrorException(
        'Something went wrong, please try again later',
      );
    }

    const prompt = basePrompt
      .replace('{sleepGoal}', sleepGoalText)
      .replace('{sleepRecords}', sleepSummary);

    // Generate highlight
    const response = await this.openAIService.generateResponse(prompt);

    const highlights = response
      .split('\n')
      .map((item) => item.replace(/^- /, '').trim())
      .filter(Boolean);

    // Store the highlight
    await this.sleepHighlightModel.create({
      userId,
      highlight_text: highlights,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: highlights,
    };
  }

  async getWeightHighlight(userId: string) {
    const today = moment().endOf('day');
    const sevenDaysAgo = moment().subtract(6, 'days').startOf('day');

    // Fetch user
    const user = await this.userModel.findById(userId).lean();
    if (!user) {
      return {
        error: true,
        statusCode: HttpStatus.NOT_FOUND,
        message: 'User not found',
      };
    }

    // Get user's physical goal
    const physicalGoalObj = user.goals?.find(
      (goal: any) => goal.goal_type === GOAL_TYPES.PHYSICAL,
    );
    const weightGoalText =
      physicalGoalObj?.selected_goal || 'No specific goal mentioned';

    // Fetch last 7 days of weight records
    const weightRecords = await this.userWeightRecordModel
      .find({
        userId,
        date: { $gte: sevenDaysAgo.toDate(), $lte: today.toDate() },
      })
      .sort({ date: 1 });

    if (weightRecords.length === 0) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: [],
      };
    }

    // Get latest record
    const latestRecord = weightRecords[weightRecords.length - 1];

    // Check if highlight already exists and is based on the latest record
    const existingHighlight = await this.weightHighlightModel.findOne({
      userId,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    if (existingHighlight) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: existingHighlight.highlight_text,
      };
    }

    // Format weight records
    const weightSummary = weightRecords
      .map(
        (record) =>
          `Date: ${moment(record.date).format('YYYY-MM-DD')}, Weight: ${record.weight || 'N/A'} kg`,
      )
      .join('; ');

    // Build prompt
    const basePrompt = process.env.WEIGHT_FEEDBACK_PROMPT;

    if (!basePrompt) {
      throw new InternalServerErrorException(
        'Something went wrong, please try again later',
      );
    }

    const prompt = basePrompt
      .replace('{goal}', weightGoalText)
      .replace('{weightRecords}', weightSummary);

    // Generate highlight from OpenAI
    const response = await this.openAIService.generateResponse(prompt);

    const highlights = response
      .split('\n')
      .map((item) => item.replace(/^- /, '').trim())
      .filter(Boolean);

    // Save new highlight
    await this.weightHighlightModel.create({
      userId,
      highlight_text: highlights,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: highlights,
    };
  }

  async getMoodHighlight(userId: string) {
    const today = moment().endOf('day');
    const sevenDaysAgo = moment().subtract(6, 'days').startOf('day');

    // Step 1: Fetch last 7 days of DailyMoodAverage
    const moodRecords = await this.dailyMoodAverageModel
      .find({
        userId,
        createdAt: { $gte: sevenDaysAgo.toDate(), $lte: today.toDate() },
      })
      .sort({ createdAt: 1 });

    if (!moodRecords.length) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: [],
      };
    }

    const latestRecord = moodRecords[moodRecords.length - 1];

    // Step 2: Check if highlights already exist
    const existingHighlight = await this.moodHighlightModel.findOne({
      userId,
      record_id: latestRecord._id,
      record_updatedAt: latestRecord.updatedAt,
    });

    if (existingHighlight) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: existingHighlight.highlight_text,
      };
    }

    // Step 3: Remap scores to mood/hunger strings
    const moodWeights = {
      happy: 5,
      'moderately happy': 4,
      sad: 3,
      irritated: 2,
      anxious: 1,
    };

    const hungerWeights = {
      high: 4,
      moderate: 3,
      mild: 2,
      barely: 1,
    };

    const getKeyByValue = (obj, val) =>
      Object.keys(obj).find((key) => obj[key] === val) || 'N/A';

    const moodSummary = moodRecords
      .map((record) => {
        const moodType = getKeyByValue(moodWeights, record.moodTypeScore);
        const hungerLevel = getKeyByValue(
          hungerWeights,
          record.hungerLevelScore,
        );
        return `Date: ${record.year}-${record.month}-${record.date}, Mood: ${moodType}, Hunger: ${hungerLevel}`;
      })
      .join('; ');

    // Step 4: Get prompt and generate new highlights
    const basePrompt = process.env.MOOD_FEEDBACK_PROMPT;
    if (!basePrompt) {
      throw new InternalServerErrorException(
        'Something went wrong, please try again later',
      );
    }
    const prompt = basePrompt.replace('{moodRecords}', moodSummary);

    const response = await this.openAIService.generateResponse(prompt);
    const highlights = response
      .split('\n')
      .map((item) => item.replace(/^- /, '').trim())
      .filter(Boolean);

    // Step 5: Save to MoodHighlight model
    await this.moodHighlightModel.create({
      userId,
      highlight_text: highlights,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: highlights,
    };
  }

  async getMealHighlight(userId: string) {
    const today = moment().endOf('day');
    const sevenDaysAgo = moment().subtract(6, 'days').startOf('day');

    // Fetch user
    const user = await this.userModel.findById(userId).lean();
    if (!user) {
      return {
        error: true,
        statusCode: HttpStatus.NOT_FOUND,
        message: 'User not found',
      };
    }

    // Get user's weight goal (or use nutrition goal if you have one)
    const nutritionGoalObj = user.goals?.find(
      (goal: any) => goal.goal_type === GOAL_TYPES.PHYSICAL, // or change to NUTRITION if needed
    );
    const userGoalText =
      nutritionGoalObj?.selected_goal || 'No specific goal mentioned';

    // Fetch the last 7 days of meal records, sorted by most recently updated
    const mealRecords = await this.userMealRecordModel
      .find({
        userId,
        date: { $gte: sevenDaysAgo.toDate(), $lte: today.toDate() },
        isDeleted: false,
      })
      .sort({ updatedAt: -1 }) // Sort by latest updated
      .lean();

    if (mealRecords.length === 0) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: [],
      };
    }

    const latestRecord = mealRecords[0];

    // Check if highlight already exists for this version of the record
    const existingHighlight = await this.mealHighlightModel.findOne({
      userId,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    if (existingHighlight) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: existingHighlight.highlight_text,
      };
    }

    // Format calorie summary
    const calorieRecords = mealRecords
      .map((record) => {
        const date = moment(record.date).format('YYYY-MM-DD');
        const totalCalories = record.meals?.reduce(
          (sum, meal) => sum + (meal.calories || 0),
          0,
        );
        return `Date: ${date}, Calories: ${totalCalories || 'N/A'}`;
      })
      .join('; ');

    // Build the prompt
    const basePrompt = process.env.MEAL_FEEDBACK_PROMPT;
    if (
      !basePrompt?.includes('{goal}') ||
      !basePrompt.includes('{calorieRecords}')
    ) {
      throw new InternalServerErrorException(
        'Something went wrong, please try again later',
      );
    }

    const prompt = basePrompt
      .replace('{goal}', userGoalText)
      .replace('{calorieRecords}', calorieRecords);

    // Generate highlight using OpenAI
    const response = await this.openAIService.generateResponse(prompt);

    const highlights = response
      .split('\n')
      .map((line) =>
        line
          .replace(/^\d+\.\s*/, '')
          .replace(/^[-*]\s*/, '')
          .trim(),
      )
      .filter(Boolean);

    // Save the generated highlight
    await this.mealHighlightModel.create({
      userId,
      highlight_text: highlights,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: highlights,
    };
  }

  async getDeviceHighlight(userId: string) {
    const today = moment().endOf('day');
    const sevenDaysAgo = moment().subtract(6, 'days').startOf('day');

    // Fetch user
    const user = await this.userModel.findById(userId).lean();
    if (!user) {
      return {
        error: true,
        statusCode: HttpStatus.NOT_FOUND,
        message: 'User not found',
      };
    }

    const deviceUsageLimit = user.deviceUsageLimit; // in hours

    // Fetch last 7 days of device usage summary
    const usageSummaries = await this.userDeviceRecordModel
      .find({
        userId,
        utcDate: { $gte: sevenDaysAgo.toDate(), $lte: today.toDate() },
        isDeleted: false,
      })
      .sort({ updatedAt: -1 })
      .lean();

    if (usageSummaries.length === 0) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: [],
      };
    }

    const latestRecord = usageSummaries[0];

    // Check for existing highlight
    const existingHighlight = await this.deviceUsageHighlightModel.findOne({
      userId,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    if (existingHighlight) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: existingHighlight.highlight_text,
      };
    }

    // Format usage summary
    const usageRecords = usageSummaries
      .map((record) => {
        const date = record.localDate;
        const usageInHours = (record.totalUsageTime || 0) / 3600; // seconds to hours
        return `Date: ${date}, Used: ${usageInHours.toFixed(2)} hrs`;
      })
      .join('; ');

    const basePrompt = process.env.DEVICE_FEEDBACK_PROMPT;
    if (
      !basePrompt?.includes('{limit}') ||
      !basePrompt.includes('{usageRecords}')
    ) {
      throw new InternalServerErrorException(
        'Something went wrong, please try again later',
      );
    }

    const prompt = basePrompt
      .replace('{limit}', `${deviceUsageLimit} hrs`)
      .replace('{usageRecords}', usageRecords);

    // Generate highlight using OpenAI
    const response = await this.openAIService.generateResponse(prompt);

    const highlights = response.split('\n').map((line) =>
      line
        .replace(/^\d+\.\s*/, '')
        .replace(/^[-*]\s*/, '')
        .trim(),
    );

    // Save to DB
    await this.deviceUsageHighlightModel.create({
      userId,
      highlight_text: highlights,
      record_id: latestRecord._id,
      record_createdAt: latestRecord.createdAt,
      record_updatedAt: latestRecord.updatedAt,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: highlights,
    };
  }
}
