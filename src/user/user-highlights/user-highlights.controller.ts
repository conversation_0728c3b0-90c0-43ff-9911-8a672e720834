import { <PERSON>, Get, Param, Req, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiParam, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { UserHighlightsService } from './user-highlights.service';
import { HighlightType } from './interface/user-highlight.interface';
import { AuthGuard } from 'src/middlewares';
import { Request } from 'express';
import { ErrorResponse } from 'src/utils/responses';
import { HighlightResponseDto } from './dto';

@ApiTags('User Highlights')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('user_highlights')
export class UserHighlightsController {
  constructor(private readonly userHighlightsService: UserHighlightsService) {}

  @ApiParam({
    name: 'type',
    enum: HighlightType,
    description: 'Type of highlight to generate',
  })
  @ApiResponse({
    status: 200,
    description: 'Highlight generated successfully.',
    type: HighlightResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid highlight type or bad request.',
    type: ErrorResponse,
  })
  @Get(':type')
  async getHighlight(@Req() req: Request, @Param('type') type: HighlightType) {
    const user = req['user'];

    return this.userHighlightsService.generateHighlight(user._id, type);
  }
}
