import { Module } from '@nestjs/common';
import { UserHighlightsService } from './user-highlights.service';
import { UserHighlightsController } from './user-highlights.controller';
import { HighLightsUtilsService } from './user-highlights.utils.service';
import { OpenAIService } from 'src/third-party/openAI/openai.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  UserSleepRecords,
  UserSleepRecordsSchema,
} from 'models/user-records/Sleep-Records';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { RepoModule } from 'src/repo/repo.module';
import {
  UserWeightRecords,
  UserWeightRecordsSchema,
  WeightHighlight,
  WeightHighlightSchema,
} from 'models/user-records/Weight-Records';
import {
  DailyMoodAverage,
  DailyMoodSchema,
  MoodHighlight,
  MoodHighlightSchema,
  UserMoodRecords,
  UserMoodRecordsSchema,
} from 'models/user-records/Mood-Records';
import {
  SleepHighlight,
  SleepHighlightSchema,
} from 'models/user-records/Sleep-Records/userSleepHighLight.schema';
import {
  MealHighlight,
  MealHighlightSchema,
  UserMealRecords,
  UserMealRecordsSchema,
} from 'models/user-records/Meal-Records';
import {
  DeviceUsageHighlight,
  DeviceUsageHighlightSchema,
  UserDeviceRecord,
  UserDeviceRecordSchema,
} from 'models/user-records/Device-records';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserSleepRecords.name, schema: UserSleepRecordsSchema },
      { name: UserWeightRecords.name, schema: UserWeightRecordsSchema },
      { name: UserMoodRecords.name, schema: UserMoodRecordsSchema },
      { name: DailyMoodAverage.name, schema: DailyMoodSchema },
      { name: MoodHighlight.name, schema: MoodHighlightSchema },
      { name: SleepHighlight.name, schema: SleepHighlightSchema },
      { name: WeightHighlight.name, schema: WeightHighlightSchema },
      { name: MealHighlight.name, schema: MealHighlightSchema },
      { name: UserMealRecords.name, schema: UserMealRecordsSchema },
      { name: DeviceUsageHighlight.name, schema: DeviceUsageHighlightSchema },
      { name: UserDeviceRecord.name, schema: UserDeviceRecordSchema },
    ]),
    ThirdPartyModule,
    CommonModule,
    AuthModule,
    RepoModule,
  ],
  controllers: [UserHighlightsController],
  providers: [UserHighlightsService, HighLightsUtilsService, OpenAIService],
})
export class UserHighlightsModule {}
