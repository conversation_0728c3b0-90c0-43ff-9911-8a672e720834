import { BadRequestException, Injectable } from '@nestjs/common';
import { HighLightsUtilsService } from './user-highlights.utils.service';
import { HighlightType } from './interface/user-highlight.interface';

@Injectable()
export class UserHighlightsService {
  constructor(
    private readonly highLightsUtilsService: HighLightsUtilsService,
  ) {}

  async generateHighlight(userId: string, type: HighlightType) {
    switch (type) {
      case HighlightType.SLEEP:
        return this.highLightsUtilsService.getSleepHighlight(userId);

      case HighlightType.WEIGHT:
        return this.highLightsUtilsService.getWeightHighlight(userId);

      case HighlightType.MOOD:
        return this.highLightsUtilsService.getMoodHighlight(userId);

      case HighlightType.NUTRITION:
        return this.highLightsUtilsService.getMealHighlight(userId);

      case HighlightType.DEVICE:
        return this.highLightsUtilsService.getDeviceHighlight(userId);

      default:
        throw new BadRequestException(
          'you need to select highlight type i.e. sleep, mood, weight, nutrition, device',
        );
    }
  }
}
