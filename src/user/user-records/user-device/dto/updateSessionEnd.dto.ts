import { IsN<PERSON>ber, IsNotEmpty, IsDateString, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { DeviceUsageSummaryDto } from './userDeviceRecords.dto';
import { BaseResponse } from 'src/utils/responses';

export class UpdateSessionEndDto {
  @ApiProperty({
    description: 'Duration of device usage in minutes ',
    example: 120,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Duration must be at least 0 minutes' })
  durationConsumed: number;

  @ApiProperty({
    description: 'ISO date-time string representing the session end time',
    example: '2025-04-23T15:45:00.000Z',
  })
  @IsNotEmpty()
  @IsDateString()
  endTime: string;
}

export class UpdateSessionEndResDto extends BaseResponse {
  @ApiProperty({
    description: 'Response message confirming session end status',
    example: 'Session ended successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Details of the updated device usage session',
    type: DeviceUsageSummaryDto,
  })
  data: DeviceUsageSummaryDto;
}
