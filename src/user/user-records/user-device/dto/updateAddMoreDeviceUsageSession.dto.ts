// updateDeviceUsageSession.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, Min } from 'class-validator';
import { DeviceUsageSummaryDto } from './userDeviceRecords.dto';
import { BaseResponse } from 'src/utils/responses';

export class UpdateAddMoreDeviceUsageSessionDto {
  @ApiProperty({
    example: '2025-04-22T08:30:00Z',
    description: 'Start time of the session in ISO format',
  })
  @IsNotEmpty()
  @IsDateString()
  startTime: string;

  @ApiProperty({
    example: 45,
    description: 'Updated duration consumed in minutes',
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Duration must be at least 0 minutes' })
  durationConsumed: number;
}

export class UpdateAddMoreDeviceUsageRecordResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Device usage session updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Updated summary with session info',
    type: DeviceUsageSummaryDto,
  })
  data: DeviceUsageSummaryDto;
}
