import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
} from 'class-validator';
import { DeviceUsageSession } from 'models/user-records/Device-records';
import { Types, Document } from 'mongoose';
import { DateTime } from 'luxon';

export class DeviceUsageSessionDto {
  @ApiProperty({
    description: 'ID of the device usage record',
    example: '661fe2b4c72f2e001fda1234',
  })
  @IsNotEmpty({ message: 'id is required' })
  id: string;

  @ApiProperty({ example: '661f00ea12cd010b9cbbd13d' })
  @IsMongoId()
  userId: Types.ObjectId;

  @ApiProperty({ example: '2025-04-22T08:30:00+05:30' })
  @IsDate()
  startTime: Date | string;

  @ApiProperty({ example: 30 })
  @IsNumber()
  durationSet: number;

  @ApiProperty({ example: 25 })
  @IsNumber()
  durationConsumed: number;

  @ApiProperty({ example: '2025-04-22T09:00:00+05:30', required: false })
  @IsDate()
  endTime?: Date | string;

  @ApiProperty({ example: false })
  @IsBoolean()
  isSessionExpired: boolean;

  static transform(
    object: DeviceUsageSession & Document,
    timezone: string = 'UTC',
  ): DeviceUsageSessionDto {
    const transformed = new DeviceUsageSessionDto();
    transformed.id = object._id.toString();
    transformed.userId = object.userId;

    // Format date to ISO string in user's timezone
    transformed.startTime = DateTime.fromJSDate(object.startTime)
      .setZone(timezone)
      .toISO();

    transformed.endTime = object.endTime
      ? DateTime.fromJSDate(object.endTime).setZone(timezone).toISO()
      : undefined;

    transformed.durationSet = object.durationSet;
    transformed.durationConsumed = object.durationConsumed;
    transformed.isSessionExpired = object.isSessionExpired;

    return transformed;
  }
}
