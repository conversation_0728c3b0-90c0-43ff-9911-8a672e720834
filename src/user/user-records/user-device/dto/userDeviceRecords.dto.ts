import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsMongoId, IsNumber, IsOptional } from 'class-validator';
import { Types } from 'mongoose';
import { DeviceUsageSessionDto } from './deviceUsageSession.dto';
import {
  DeviceUsageSession,
  UserDeviceRecord,
} from 'models/user-records/Device-records';

export class DeviceUsageSummaryDto {
  @ApiProperty({ example: '661f00ea12cd010b9cbbd13d' })
  @IsMongoId()
  userId: Types.ObjectId;

  @ApiProperty({ example: 120 })
  @IsNumber()
  deviceUsageLimit: number;

  @ApiProperty({ example: 90 })
  @IsNumber()
  totalUsageTime: number;

  @ApiProperty({ example: '2025-04-22T00:00:00Z' })
  @IsDate()
  date: string;

  @ApiProperty({
    type: [DeviceUsageSessionDto],
    required: false,
    description: 'Device usage sessions for the given date/user',
  })
  @IsOptional()
  sessions?: DeviceUsageSessionDto[];

  static transform(
    object: UserDeviceRecord,
    sessions: DeviceUsageSession[] = [],
  ): DeviceUsageSummaryDto {
    const transformed = new DeviceUsageSummaryDto();
    transformed.userId = object.userId;
    transformed.deviceUsageLimit = object.deviceUsageLimit;
    transformed.totalUsageTime = object.totalUsageTime;
    transformed.date = object.localDate;

    if (sessions.length) {
      transformed.sessions = sessions.map((session) =>
        DeviceUsageSessionDto.transform(session),
      );
    }

    return transformed;
  }
}
