import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { DeviceUsageAverageDTO } from './averageDeviceUsage.dto';

export class GetMonthlyUserDeviceUsageAvgRecordDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of device usage records',
    example: 12,
  })
  total: number;

  @ApiProperty({
    description: 'List of monthly device usage records',
    type: [DeviceUsageAverageDTO],
  })
  data: DeviceUsageAverageDTO[];
}
