import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON><PERSON>, Min, IsNotEmpty } from 'class-validator';
import { BaseResponse } from 'src/utils/responses';
import { DeviceUsageSessionDto } from './deviceUsageSession.dto';

export class SyncUpdateDeviceUsageSessionDto {
  @ApiProperty({
    description: 'Duration consumed during the device usage session',
    type: Number,
    example: 90,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Duration must be at least 0 minutes' })
  durationConsumed?: number;
}

export class SyncUpdateDeviceUsageSessionResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Device usage session updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Updated device usage session details',
    type: DeviceUsageSessionDto,
  })
  data: DeviceUsageSessionDto;
}
