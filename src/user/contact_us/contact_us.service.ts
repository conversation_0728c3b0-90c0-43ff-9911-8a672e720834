import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CONTACTUS_ATTACHMENTS_TYPES,
  ContactUsQuery,
} from 'models/contact-us-query';
import { User } from 'models/user';
import { Model } from 'mongoose';

import {
  ContactUsQueryDTO,
  PostContactUsQueryReqDTO,
  PostContactUsQueryResDTO,
} from './dto';
import {
  CustomConfigService,
  EmailService,
  EncryptionService,
} from 'src/common/services';
import { FileUploadService } from '../file-upload/file-upload.service';

@Injectable()
export class ContactUsService {
  constructor(
    private readonly emailServce: EmailService,
    private readonly encryptionService: EncryptionService,
    private readonly fileUploadService: FileUploadService,

    @InjectModel(ContactUsQuery.name)
    private contactUsQueryModel: Model<ContactUsQuery>,
  ) {}

  async postContactUsQuery(
    user: User,
    postData: PostContactUsQueryReqDTO,
    attachments: Express.Multer.File[] = [],
  ): Promise<PostContactUsQueryResDTO> {
    const { category, description } = postData; // data will be validated via dtos

    const createData: any = {
      category,
      description,
      userId: user._id,
    };

    if (attachments.length > 0) {
      // Concurrent file processing: scan and upload files
      const uploadedFiles = await Promise.all(
        attachments.map(async (file) => {
          const type = file.mimetype.split('/')[0];

          if (type.toUpperCase() in CONTACTUS_ATTACHMENTS_TYPES) {
            // Use FileUploadService for ClamAV scanning and S3 upload
            const uploadedFile =
              await this.fileUploadService.uploadAndStoreFile(file);

            return {
              fileId: uploadedFile._id.toString(),
              type,
            };
          } else {
            throw new BadRequestException(`${type} files are not supported.`);
          }
        }),
      );

      createData.attachments = uploadedFiles;
    }

    const new_query = await this.contactUsQueryModel.create(createData);

    const updatedQuery: any = { ...new_query.toObject(), user };

    const {
      subject,
      emailBody,
      fromEmail,
      contactUsQueryConfirmationEmailTemplate,
    } = CustomConfigService.PROPERTIES.contactUsQueryConfirmationEmail;

    const html = contactUsQueryConfirmationEmailTemplate.replace(
      '$$name',
      user.name,
    );

    await this.emailServce.sendTextMail({
      fromEmail,
      toEmail: user.email,
      subject,
      html,
      textBody: emailBody,
    });

    const resp = ContactUsQueryDTO.transform(updatedQuery);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }

  /**
   * Get file URLs for contact us query attachments
   * @param fileIds Array of file IDs to get URLs for
   * @returns Array of file URLs with metadata
   */
  async getFileUrls(
    fileIds: string[],
  ): Promise<Array<{ fileId: string; url?: string; error?: string }>> {
    if (!fileIds || fileIds.length === 0) {
      return [];
    }

    return Promise.all(
      fileIds.map(async (fileId) => {
        try {
          const url = await this.fileUploadService.getValidS3Url(fileId);
          return { fileId, url };
        } catch {
          return { fileId, error: 'File not accessible' };
        }
      }),
    );
  }

  /**
   * Get contact us query with file URLs populated
   * @param queryId Contact us query ID
   * @returns Contact us query with file URLs
   */
  async getContactUsQueryWithFiles(queryId: string) {
    const query = await this.contactUsQueryModel
      .findById(queryId)
      .populate('userId')
      .exec();

    if (!query) {
      throw new BadRequestException(`Query with ID ${queryId} not found.`);
    }

    const queryObj = query.toObject();

    // Get file URLs for attachments
    if (queryObj.attachments && queryObj.attachments.length > 0) {
      const fileIds = queryObj.attachments
        .map((attachment) => attachment.fileId)
        .filter(Boolean);
      const fileUrls = await this.getFileUrls(fileIds);

      // Map URLs back to attachments
      queryObj.attachments = queryObj.attachments.map((attachment) => {
        const fileUrlData = fileUrls.find(
          (f) => f.fileId === attachment.fileId,
        );
        return {
          ...attachment.toObject(),
          url: fileUrlData?.url || null,
        };
      });
    }

    return queryObj;
  }
}
