import { Module } from '@nestjs/common';
import { ContactUsController } from './contact_us.controller';
import { ContactUsService } from './contact_us.service';
import { ContactUsQuery, ContactUsQuerySchema } from 'models/contact-us-query';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { FileUploadModule } from '../file-upload/file-upload.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ContactUsQuery.name, schema: ContactUsQuerySchema },
    ]),
    AuthModule,
    RepoModule,
    ThirdPartyModule,
    CommonModule,
    FileUploadModule,
  ],
  controllers: [ContactUsController],
  providers: [ContactUsService],
})
export class ContactUsModule {}
