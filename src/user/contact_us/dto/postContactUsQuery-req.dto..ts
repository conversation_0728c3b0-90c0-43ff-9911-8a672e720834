import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { QUERY_CATEGORY } from 'models/contact-us-query';

export class PostContactUsQueryReqDTO {
  @ApiProperty({ enum: QUERY_CATEGORY, description: 'Category of the query' })
  @IsEnum(QUERY_CATEGORY)
  @IsNotEmpty()
  category: QUERY_CATEGORY;

  @ApiProperty({
    description: 'Description of the query',
    example: 'I need help with my order.',
  })
  @IsString({ message: 'Description must be a string' })
  @IsNotEmpty({ message: 'Description is required' })
  description: string;
}
