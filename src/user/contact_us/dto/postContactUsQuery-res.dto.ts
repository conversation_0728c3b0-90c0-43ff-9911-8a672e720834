import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject } from 'class-validator';
import { BaseResponse } from 'src/utils/responses';
import { ContactUsQueryDTO } from './ContactUsQuery.dto';

export class PostContactUsQueryResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The contact us query details.',
    type: ContactUsQueryDTO,
  })
  @IsNotEmpty()
  @IsObject()
  data: ContactUsQueryDTO;
}
