import {
  Body,
  Controller,
  Post,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { ContactUsService } from './contact_us.service';
import { PostContactUsQueryReqDTO } from './dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AllowedMixEntensions, getMulterMediaOptions } from 'src/utils/multer';

@ApiTags('User-Contact-Us')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('contact')
export class ContactUsController {
  constructor(private readonly contactUsService: ContactUsService) {}

  @ApiOperation({ summary: 'Submit a contact us query' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Contact Us payload including optional file attachments',
    type: PostContactUsQueryReqDTO,
  })
  @ApiResponse({
    status: 201,
    description: 'Query submitted successfully',
  })
  @Post()
  @UseInterceptors(
    FilesInterceptor(
      'attachments',
      3,
      getMulterMediaOptions({
        fileSize: 20,
        fileExtensions: AllowedMixEntensions,
      }),
    ),
  )
  async PostContactQuery(
    @Req() req: Request,
    @Body() postData: PostContactUsQueryReqDTO,
    @UploadedFiles() attachments: Express.Multer.File[],
  ) {
    const user = req['user'];

    return this.contactUsService.postContactUsQuery(
      user,
      postData,
      attachments,
    );
  }
}
