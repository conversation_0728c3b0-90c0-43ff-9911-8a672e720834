import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, MinLength } from 'class-validator';
import { Carousel, CAROUSEL_TAGS } from 'models/carousel/carousel.schema';

export class CarouselDto {
  @ApiProperty({ example: '665fdaf3cf304914b8976bc0' })
  id: string;

  @ApiProperty({ example: '20250606T000000' })
  @IsString()
  userId: string;

  @ApiProperty({ example: 'todaytip', enum: CAROUSEL_TAGS })
  @IsEnum(CAROUSEL_TAGS)
  tag: CAROUSEL_TAGS;

  @ApiProperty({
    example: 'https://yourcdn.com/carousel/todaytip/2025-06-06-img.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({ example: '2025-06-06T00:00:00.000Z' })
  @IsString()
  date: string;

  @ApiProperty({ example: 'Good Morning!' })
  @IsString()
  @MinLength(3)
  greetings: string;

  @ApiProperty({ example: 'Here’s your wellness tip' })
  @IsString()
  @MinLength(3)
  subheading: string;

  @ApiProperty({
    example: 'Drink a glass of water right after waking up to stay hydrated.',
  })
  @IsString()
  @MinLength(5)
  content: string;

  @ApiProperty({ example: '2025-06-06T04:30:00.000Z' })
  @IsString()
  createdAt: string;

  @ApiProperty({ example: '2025-06-06T05:00:00.000Z' })
  @IsString()
  updatedAt: string;

  static transform(object: Carousel): CarouselDto {
    const transformedObj = new CarouselDto();
    transformedObj.id = object._id.toString();
    transformedObj.tag = object.tag;
    transformedObj.image = object.image;
    transformedObj.date = object.date.toISOString();
    transformedObj.greetings = object.greetings;
    transformedObj.subheading = object.subheading;
    transformedObj.content = object.content;
    transformedObj.createdAt = object.createdAt.toISOString();
    transformedObj.updatedAt = object.updatedAt.toISOString();
    return transformedObj;
  }
}
