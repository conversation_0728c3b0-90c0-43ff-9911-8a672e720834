import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { CarouselService } from './carousel.service';
import { GetCarouselResDto } from './dto';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';

@ApiTags('Carousel')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('carousel')
export class CarouselController {
  constructor(private readonly carouselService: CarouselService) {}

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved carousel highlights',
    type: GetCarouselResDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponse,
  })
  async getDailyCarouselHighlights(
    @Req() req: Request,
  ): Promise<GetCarouselResDto> {
    const user = req['user'];
    return this.carouselService.getDailyCarouselHighlights(user._id);
  }
}
