import { Module } from '@nestjs/common';
import { CarouselService } from './carousel.service';
import { CarouselController } from './carousel.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';
import { CommonModule } from 'src/common/common.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { Carousel, CarouselSchema } from 'models/carousel/carousel.schema';
import {
  UserWeightRecords,
  UserWeightRecordsSchema,
} from 'models/user-records/Weight-Records';
import { User, UserSchema } from 'models/user';
import { UserRecordsModule } from '../user-records/user-records.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Carousel.name, schema: CarouselSchema },
      { name: UserWeightRecords.name, schema: UserWeightRecordsSchema },
      { name: User.name, schema: UserSchema },
    ]),
    AuthModule,
    RepoModule,
    CommonModule,
    ThirdPartyModule,
    UserRecordsModule,
  ],
  controllers: [CarouselController],
  providers: [CarouselService],
})
export class CarouselModule {}
