import {
  Injectable,
  InternalServerErrorException,
  HttpStatus,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as moment from 'moment';
import { GOAL_TYPES, User } from 'models/user';
import { CarouselDto } from './dto/carousel.dto';
import { UserWeightRecords } from 'models/user-records/Weight-Records';
import { Carousel, CAROUSEL_TAGS } from 'models/carousel/carousel.schema';
import { OpenAIService } from 'src/third-party/openAI/openai.service';
import { GetCarouselResDto } from './dto';

@Injectable()
export class CarouselService {
  constructor(
    @InjectModel(User.name) private readonly userModel: Model<User>,
    @InjectModel(UserWeightRecords.name)
    private readonly userWeightModel: Model<UserWeightRecords>,
    @InjectModel(Carousel.name) private readonly carouselModel: Model<Carousel>,
    private readonly openAIService: OpenAIService,
  ) {}

  // Add an array of 7 valid, royalty-free image URLs
  private readonly CAROUSEL_IMAGE_URLS = [
    'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1501854140801-50d01698950b?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?auto=format&fit=crop&w=800&q=80',
  ];

  async getDailyCarouselHighlights(userId: string): Promise<GetCarouselResDto> {
    const today = moment().startOf('day').toDate();
    const tomorrow = moment().add(1, 'day').startOf('day').toDate();

    const existingCarousels = await this.carouselModel.find({
      userId,
      date: { $gte: today, $lt: tomorrow },
    });

    if (existingCarousels.length) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: existingCarousels.map(CarouselDto.transform),
      };
    }

    const user = await this.userModel.findById(userId).lean();
    if (!user) {
      throw new InternalServerErrorException('User not found');
    }

    const momentGoal =
      user.goals?.find((goal: any) => goal.goal_type === GOAL_TYPES.PHYSICAL) ||
      'Stay fit and feel light';

    const pastWeek = {
      $gte: moment().subtract(6, 'days').startOf('day').toDate(),
      $lte: moment().endOf('day').toDate(),
    };

    const weightRecords = await this.userWeightModel
      .find({ userId, date: pastWeek })
      .sort({ date: 1 })
      .lean();

    const weightText = weightRecords.length
      ? weightRecords
          .map(
            (r) =>
              `Date: ${moment(r.date).format('YYYY-MM-DD')}, Weight: ${r.weight ?? 'N/A'} kg`,
          )
          .join('; ')
      : 'No data available';

    const basePrompt = process.env.WEIGHT_FEEDBACK_PROMPT;
    if (!basePrompt) {
      throw new InternalServerErrorException('OpenAI prompt missing');
    }

    const prompt = basePrompt
      .replace('{goal}', String(momentGoal))
      .replace('{weightRecords}', weightText);

    let userUpdateHighlight = '';
    let motivationalLine = '';

    try {
      const aiResponse = await this.openAIService.generateResponse(prompt);

      [userUpdateHighlight, motivationalLine] = aiResponse
        .split('\n')
        .map((t) => t.trim());

      if (!userUpdateHighlight || !motivationalLine) {
        throw new Error('Incomplete AI response');
      }
    } catch {
      userUpdateHighlight = 'Keep tracking your progress regularly!';
      motivationalLine = 'Every step counts toward your goal!';
    }

    // Extra safeguard for userUpdateHighlight
    if (!userUpdateHighlight || !userUpdateHighlight.trim()) {
      console.error('userUpdateHighlight is empty, using fallback.');
      userUpdateHighlight = 'Keep tracking your progress regularly!';
    }

    const tipPrompt = process.env.TODAY_TIP_PROMPT || '';

    let tipGreeting = '';
    let tipSubheading = '';
    let tipContent = '';
    let tipImage = '';

    try {
      const tipResponse = await this.openAIService.generateResponse(tipPrompt);
      const [greeting, subheading, content] = tipResponse
        .split('\n')
        .map((line) => line.trim().replace(/^['"\d\.\-\s]+/, ''));

      if (!greeting || !subheading || !content) {
        throw new Error('Incomplete Tip AI response');
      }

      tipGreeting = greeting;
      tipSubheading = subheading;
      // Sanitize content to remove any URLs
      tipContent = content.replace(/https?:\/\/\S+/g, '').trim();
      // Always select a random image from the array
      tipImage =
        this.CAROUSEL_IMAGE_URLS[
          Math.floor(Math.random() * this.CAROUSEL_IMAGE_URLS.length)
        ];
    } catch {
      tipGreeting = 'Protein Power';
      tipSubheading = 'Add more protein to your meals for better results';
      tipContent =
        'Including protein in every meal helps control hunger and supports muscle recovery after workouts.';
      tipImage =
        this.CAROUSEL_IMAGE_URLS[
          Math.floor(Math.random() * this.CAROUSEL_IMAGE_URLS.length)
        ];
    }

    // Extra safeguard for tipContent
    if (!tipContent || !tipContent.trim()) {
      console.error('Tip content is empty, using fallback.');
      tipContent = 'Stay healthy! Remember to take care of yourself.';
    }

    const carouselData: Partial<Carousel>[] = [
      {
        tag: CAROUSEL_TAGS.UPDATE,
        date: today,
        greetings: `Hello ${user.name?.split(' ')[0] || 'there'}!`,
        subheading: motivationalLine,
        content: userUpdateHighlight,
        userId: userId,
      },
      {
        tag: CAROUSEL_TAGS.TODAYTIP,
        date: today,
        greetings: tipGreeting,
        subheading: tipSubheading,
        content: tipContent,
        image: tipImage,
        userId: userId,
      },
    ];

    const created = await this.carouselModel.insertMany(carouselData);

    const resp = created.map((doc) => CarouselDto.transform(doc as Carousel));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
