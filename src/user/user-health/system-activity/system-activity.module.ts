import { Module } from '@nestjs/common';
import { SystemActivityController } from './system-activity.controller';
import { SystemActivityService } from './system-activity.service';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [CommonModule, RepoModule, AuthModule, ConfigModule],
  controllers: [SystemActivityController],
  providers: [SystemActivityService],
})
export class SystemActivityModule {}
