import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { SystemActivityService } from './system-activity.service';
import {
  AddHealthSystemAggregateReqDTO,
  AddHealthSystemAggregateResDTO,
} from './dto';
import { AuthGuard } from 'src/middlewares';
import {
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
} from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { Request } from 'express';

@ApiTags('User-Health')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('user-health')
export class SystemActivityController {
  constructor(private readonly systemActivityService: SystemActivityService) {}
  //inplement swagger api including request body and response body

  @ApiOperation({
    summary: 'Add system activity data',
  })
  @ApiBody({
    type: AddHealthSystemAggregateReqDTO,
    description: 'Payload for adding system activity data',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully added system activity data.',
    type: AddHealthSystemAggregateResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('system-activity')
  async addSystemActivity(
    @Req() req: Request,
    @Body() body: AddHealthSystemAggregateReqDTO,
  ) {
    const user = req['user'];
    return this.systemActivityService.addSystemAggregate(user, body);
  }
}
