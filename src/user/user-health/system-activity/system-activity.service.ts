import { Injectable } from '@nestjs/common';
import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import {
  AddHealthSystemAggregateReqDTO,
  AddHealthSystemAggregateResDTO,
} from './dto';
import { User } from 'models/user';
import { BadRequestException, HttpStatus } from '@nestjs/common';

@Injectable()
export class SystemActivityService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}/health`;

    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }
  async addSystemAggregate(
    user: User,
    dto: AddHealthSystemAggregateReqDTO,
  ): Promise<AddHealthSystemAggregateResDTO> {
    const timeZone = user.timeZone;
    if (!timeZone) {
      throw new BadRequestException('User time zone is missing.');
    }
    const { _id: userId } = user;
    const { os, source } = dto;

    // Validate source field
    if (!source) {
      throw new BadRequestException('Source field is required.');
    }

    const payload = {
      ...dto,
      userId,
      timeZone,
    };

    const data = await this.microserviceClient.post(
      this.apiKey,
      `${this.MICROSERVICE_URL}/activity-system`,
      payload,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.message ||
          data?.resp?.data?.message ||
          'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,

      userId: userId.toString(),
      timeZone,
      os,
      source,
      message: data?.resp?.message || null,
      success: data?.resp?.success || false,
      updatedCount: data?.resp?.updatedCount || 0,
      insertedCount: data?.resp?.insertedCount || 0,

      dumps: dto.dumps,
    };
  }
}
