import {
  IsArray,
  IsNotEmpty,
  IsString,
  ValidateNested,
  IsBoolean,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

class AggregateDataItem {
  @ApiProperty({
    description: 'Time of the data entry in yyyy-mm-dd format',
    example: '2025-05-20',
  })
  @IsNotEmpty()
  @IsString()
  time: string;

  @IsOptional()
  @IsNumber()
  distance?: number;

  @IsOptional()
  @IsString()
  measurement?: string;

  @IsOptional()
  @IsNumber()
  totalSteps?: number;

  @IsOptional()
  @IsNumber()
  totalCalories?: number;

  @IsOptional()
  @IsNumber()
  min?: number;

  @IsOptional()
  @IsNumber()
  max?: number;
}

class SystemAggregateEntry {
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Array of data items',
    type: [AggregateDataItem],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AggregateDataItem)
  data: AggregateDataItem[];
}

export class AddHealthSystemAggregateResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Unique identifier of the user.',
    example: '609e1253c25e4a2f8c6b3d42',
  })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Time zone of the user.',
    example: 'America/New_York',
  })
  @IsNotEmpty()
  @IsString()
  timeZone: string;

  @ApiProperty({
    description: 'Operating system of the user',
    example: 'Android',
  })
  @IsNotEmpty()
  @IsString()
  os: string;

  @ApiProperty({
    description: 'Source of the data',
    example: 'Apple',
  })
  @IsNotEmpty()
  @IsString()
  source: string;

  @ApiProperty({
    description: 'Message from the server',
    example: 'System aggregate data processed successfully.',
  })
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Success status of the request',
    example: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  success: boolean;

  @ApiProperty({
    description: 'Number of records updated',
    example: 10,
  })
  @IsNotEmpty()
  @IsNumber()
  updatedCount: number;

  @ApiProperty({
    description: 'Number of records inserted',
    example: 5,
  })
  @IsNotEmpty()
  @IsNumber()
  insertedCount: number;

  @ApiProperty({
    description: 'Array of system aggregate entries',
    type: [SystemAggregateEntry],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SystemAggregateEntry)
  dumps: SystemAggregateEntry[];
}
