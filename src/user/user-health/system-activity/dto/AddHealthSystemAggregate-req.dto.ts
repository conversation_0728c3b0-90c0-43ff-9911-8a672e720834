//implement swagger api
import {
  <PERSON>Array,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

class AggregateDataItem {
  @ApiProperty({
    description: 'Time of the data entry in yyyy-mm-dd format',
    example: '2025-05-20',
  })
  @IsNotEmpty()
  @IsString()
  time: string;

  @IsOptional()
  @IsNumber()
  distance?: number;

  @IsOptional()
  @IsString()
  measurement?: string;

  @IsOptional()
  @IsNumber()
  totalSteps?: number;

  @IsOptional()
  @IsNumber()
  totalCalories?: number;

  @IsOptional()
  @IsNumber()
  min?: number;

  @IsOptional()
  @IsNumber()
  max?: number;
}

class SystemAggregateEntry {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AggregateDataItem)
  data: AggregateDataItem[];
}

export class AddHealthSystemAggregateReqDTO {
  @ApiProperty({
    description: 'Operating system of the user',
    example: 'Android',
  })
  @IsNotEmpty()
  @IsString()
  os: string;

  @ApiProperty({
    description: 'deviceId of the user',
    example: '12345abcde67890fghij',
  })
  @IsNotEmpty()
  @IsString()
  deviceId: string;

  @ApiProperty({
    description: 'Source of the data',
    example: 'Apple',
  })
  @IsNotEmpty()
  @IsString()
  source: string;

  @ApiProperty({
    description: 'Array of system aggregate entries',
    type: [SystemAggregateEntry],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SystemAggregateEntry)
  dumps: SystemAggregateEntry[];
}
