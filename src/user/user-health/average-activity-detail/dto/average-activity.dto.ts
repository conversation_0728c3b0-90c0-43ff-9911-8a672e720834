import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export class AverageActivityDTO {
  @ApiProperty({ description: 'Name of the activity (e.g., Running, Walking)' })
  @IsString()
  @IsNotEmpty()
  activityName: string;

  @ApiProperty({ description: 'averageStepsCount (e.g., Running, Walking)' })
  @IsString()
  @IsNotEmpty()
  averageSteps: string;

  @ApiProperty({
    description:
      'Average calories burned for the activity over the time period',
  })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  averageCaloriesBurned: number;

  static transform(object: any): AverageActivityDTO {
    const transformedObj = new AverageActivityDTO();
    transformedObj.activityName = object.activityName;
    transformedObj.averageCaloriesBurned = object.averageCaloriesBurned;
    transformedObj.averageSteps = object.averageSteps;
    return transformedObj;
  }
}
