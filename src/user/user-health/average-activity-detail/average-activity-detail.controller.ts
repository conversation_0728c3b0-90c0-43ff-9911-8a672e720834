import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ActivityRecordFilter } from './interface/userActivityDetail.interface';
import { AverageActivityDetailService } from './average-activity-detail.service';
import { AuthGuard } from 'src/middlewares';

@UseGuards(AuthGuard)
@Controller('activity-detail')
export class AverageActivityDetailController {
  constructor(
    private readonly averageActivityDetailService: AverageActivityDetailService,
  ) {}

  @Get()
  async getAverageActivityState(
    @Query('filter') query: ActivityRecordFilter,
    @Query('userId') userId: string,
  ) {
    return this.averageActivityDetailService.getAverageActivityState(
      userId,
      query,
    );
  }
}
