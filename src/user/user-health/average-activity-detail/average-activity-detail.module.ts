import { Module } from '@nestjs/common';
import { AverageActivityDetailService } from './average-activity-detail.service';
import { AverageActivityDetailController } from './average-activity-detail.controller';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [CommonModule, RepoModule, AuthModule, ConfigModule],
  controllers: [AverageActivityDetailController],
  providers: [AverageActivityDetailService],
})
export class AverageActivityDetailModule {}
