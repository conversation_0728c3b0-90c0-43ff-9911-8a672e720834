import { ApiProperty } from '@nestjs/swagger';

export enum PeriodEnum {
  WEEK = 'weekly',
  MONTH = 'monthly',
  HALF_YEARLY = 'half_yearly',
  YEAR = 'yearly',
}

export class DailyAnalyticsDto {
  @ApiProperty()
  date: string;

  @ApiProperty()
  burnedCalories: number;
}

export class WeeklyBreakdownDto {
  @ApiProperty()
  weekStart: string;

  @ApiProperty()
  weekEnd: string;

  @ApiProperty()
  averageBurnedCalories: number;

  @ApiProperty()
  days: number;
}

export class MonthlyAnalyticsDto {
  @ApiProperty()
  year: number;

  @ApiProperty()
  month: number;

  @ApiProperty()
  averageBurnedCalories: number;
}

// Wrapper for `week` response
export class WeeklyAnalyticsResponseDto {
  @ApiProperty({ type: [DailyAnalyticsDto] })
  data: DailyAnalyticsDto[];
}

// Wrapper for `month` response
export class MonthlyGroupedByWeeksResponseDto {
  @ApiProperty({ type: () => [WeeklyBreakdownDto] })
  data: {
    weeks: WeeklyBreakdownDto[];
  };
}

// Wrapper for `six-month` or `year` response
export class MonthlyAnalyticsResponseListDto {
  @ApiProperty({ type: [MonthlyAnalyticsDto] })
  data: MonthlyAnalyticsDto[];
}

// Base Response Wrapper
export class BaseActivityAnalyticsResponse<T> {
  @ApiProperty({ example: false })
  error: boolean;

  @ApiProperty({ example: 200 })
  statusCode: number;

  //add total
  @ApiProperty({ example: 6 })
  total: number;

  @ApiProperty()
  data: T;
}
