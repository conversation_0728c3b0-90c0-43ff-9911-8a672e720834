import { Controller } from '@nestjs/common';
import { HealthAnalyticsService } from './health-analytics.service';
import { Get, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';
// Update the import path or file name if necessary
import { BaseActivityAnalyticsResponse } from '../dto/healthAnalytics-res.dto';
import { DailyAnalyticsDto } from '../dto/healthAnalytics-res.dto';
import { WeeklyBreakdownDto } from '../dto/healthAnalytics-res.dto';
import { MonthlyAnalyticsDto } from '../dto/healthAnalytics-res.dto';
import { PeriodEnum } from '../dto/healthAnalytics-res.dto';

@ApiTags('Health-Analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('health-analytics')
export class HealthAnalyticsController {
  constructor(
    private readonly healthAnalyticsService: HealthAnalyticsService,
  ) {}

  @ApiQuery({
    name: 'period',
    required: true,
    enum: PeriodEnum,
  })
  @ApiResponse({
    status: 200,
    description: 'Activity analytics for "week" period',
    type: BaseActivityAnalyticsResponse<DailyAnalyticsDto[]>,
  })
  @ApiResponse({
    status: 200,
    description: 'Activity analytics for "month" period',
    type: BaseActivityAnalyticsResponse<{ weeks: WeeklyBreakdownDto[] }>,
  })
  @ApiResponse({
    status: 200,
    description: 'Activity analytics for "six-month" or "year" period',
    type: BaseActivityAnalyticsResponse<MonthlyAnalyticsDto[]>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Missing required parameters.',
  })
  @UseGuards(AuthGuard)
  @Get('activity-analytics')
  async getActivityAnalytics(@Req() req, @Query('period') period: PeriodEnum) {
    const user = req.user;
    return this.healthAnalyticsService.getActivityAnalytics(user._id, period);
  }
}
