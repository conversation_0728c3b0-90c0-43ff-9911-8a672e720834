import { Modu<PERSON> } from '@nestjs/common';
import { HealthAnalyticsService } from './health-analytics.service';
import { HealthAnalyticsController } from './health-analytics.controller';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [CommonModule, RepoModule, AuthModule, ConfigModule],
  providers: [HealthAnalyticsService],
  controllers: [HealthAnalyticsController],
})
export class HealthAnalyticsModule {}
