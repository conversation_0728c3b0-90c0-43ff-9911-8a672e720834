import { Injectable } from '@nestjs/common';
import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';

import { BadRequestException, HttpStatus } from '@nestjs/common';

@Injectable()
export class HealthAnalyticsService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}/analytics`;

    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }

  async getActivityAnalytics(
    userId: string,
    period: 'weekly' | 'monthly' | 'half_yearly' | 'yearly',
  ): Promise<any> {
    if (!userId || !period) {
      throw new BadRequestException('userId and period are required');
    }

    const url = `${this.MICROSERVICE_URL}/activity-analytics?userId=${userId}&period=${period}`;
    const data = await this.microserviceClient.get(this.apiKey, url);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: data?.resp?.total || 0,
      data: data?.resp?.data || null,
    };
  }
}
