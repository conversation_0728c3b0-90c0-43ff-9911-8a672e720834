import { Injectable, BadRequestException, HttpStatus } from '@nestjs/common';
import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import { GetHealthHighlightResDTO } from './dto';

@Injectable()
export class HealthHighlightService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}/health-highlight`;
    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }

  async getHighlight(
    userId: string,
    movementGoal: string,
  ): Promise<GetHealthHighlightResDTO> {
    const data = await this.microserviceClient.post(
      this.apiKey,
      this.MICROSERVICE_URL,
      { userId, movementGoal },
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
    };
  }
}
