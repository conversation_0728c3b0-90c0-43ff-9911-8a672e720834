import { Module } from '@nestjs/common';
import { HealthHighlightService } from './health-highlight.service';
import { HealthHighlightController } from './health-highlight.controller';
import { HttpModule } from '@nestjs/axios';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [HttpModule, CommonModule, RepoModule, AuthModule, ConfigModule],
  controllers: [HealthHighlightController],
  providers: [HealthHighlightService],
})
export class HealthHighlightModule {}
