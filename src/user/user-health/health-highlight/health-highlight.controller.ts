import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { GetHealthHighlightResDTO } from './dto';
import { HealthHighlightService } from './health-highlight.service';
import { AuthGuard } from 'src/middlewares';
import { GOAL_TYPES } from 'models/user';

@ApiBearerAuth()
@ApiTags('Health Highlight')
@UseGuards(AuthGuard)
@Controller('health/highlight')
export class HealthHighlightController {
  constructor(private readonly highlightService: HealthHighlightService) {}

  @ApiOperation({
    summary: 'Get Health Highlight',
    description:
      'Retrieve the health highlight data for the authenticated user based on their movement goal.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved health highlight data.',
    type: GetHealthHighlightResDTO,
  })
  @Get()
  async getHighlight(@Req() req: Request): Promise<GetHealthHighlightResDTO> {
    const user = req['user'];
    const userId = user._id;

    const movementGoalObj = user.goals?.find(
      (goal: any) => goal.goal_type === GOAL_TYPES.MOVEMENT,
    );

    const movementGoal = movementGoalObj?.selected_goal || 1000;

    return this.highlightService.getHighlight(userId, movementGoal);
  }
}
