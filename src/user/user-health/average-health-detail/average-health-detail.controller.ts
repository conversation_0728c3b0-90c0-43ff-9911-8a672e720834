import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AverageHealthDetailService } from './average-health-detail.service';
import { HealthRecordFilter } from './interface/userHealthDetail.interface';
import { AuthGuard } from 'src/middlewares';

@UseGuards(AuthGuard)
@Controller('health-detail')
export class AverageHealthDetailController {
  constructor(
    private readonly averageHealthDetailService: AverageHealthDetailService,
  ) {}

  @Get()
  async getAverageHealthState(
    @Query('filter') query: HealthRecordFilter,
    @Query('userId') userId: string,
  ) {
    return this.averageHealthDetailService.getAverageHealthState(userId, query);
  }
}
