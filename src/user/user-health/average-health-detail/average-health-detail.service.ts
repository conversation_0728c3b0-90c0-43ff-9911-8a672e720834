import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MicroserviceHttpClientService } from 'src/common/services';
import { HealthRecordFilter } from './interface/userHealthDetail.interface';
import { GetAverageHealthStatsResDTO } from './dto';

@Injectable()
export class AverageHealthDetailService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}`;

    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }

  async getAverageHealthState(
    userId: string,
    filter: HealthRecordFilter,
  ): Promise<GetAverageHealthStatsResDTO> {
    if (!userId || !filter) {
      throw new BadRequestException('userId or filter are required');
    }

    const url = `${this.MICROSERVICE_URL}/admin/userHealth/details?userId=${userId}&filter=${filter}`;
    const data = await this.microserviceClient.get(this.apiKey, url);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
    };
  }
}
