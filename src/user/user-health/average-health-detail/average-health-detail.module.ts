import { Module } from '@nestjs/common';
import { AverageHealthDetailService } from './average-health-detail.service';
import { AverageHealthDetailController } from './average-health-detail.controller';
import { CommonModule } from 'src/common/common.module';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
  imports: [CommonModule, RepoModule, AuthModule, ConfigModule],
  controllers: [AverageHealthDetailController],
  providers: [AverageHealthDetailService],
})
export class AverageHealthDetailModule {}
