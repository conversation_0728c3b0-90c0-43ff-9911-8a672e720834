import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export enum AVERAGE_STATS_PERIOD {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class AverageHealthStatsDTO {
  @ApiProperty({ description: 'Average calories burned for the time period' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  averageCaloriesBurned: number;

  @ApiProperty({ description: 'Total steps count for the time period' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  totalStepsCount: number;

  @ApiProperty({ description: 'Average heart rate for the time period' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  averageHeartRate: number;

  static transform(object: any): AverageHealthStatsDTO {
    const transformedObj = new AverageHealthStatsDTO();
    transformedObj.averageCaloriesBurned = object.averageCaloriesBurned;
    transformedObj.totalStepsCount = object.totalStepsCount;
    transformedObj.averageHeartRate = object.averageHeartRate;
    return transformedObj;
  }
}
