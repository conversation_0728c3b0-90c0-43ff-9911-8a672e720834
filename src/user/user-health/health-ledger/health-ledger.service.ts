import { Injectable } from '@nestjs/common';
import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, HttpStatus } from '@nestjs/common';

@Injectable()
export class HealthLedgerService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}/health-ledger`;

    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }

  async getHealthLedgerData(userId: string, timeZone: string) {
    const data = await this.microserviceClient.get(
      this.api<PERSON>ey,
      `${this.MICROSERVICE_URL}/${userId}?timeZone=${encodeURIComponent(timeZone)}`,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
    };
  }
}
