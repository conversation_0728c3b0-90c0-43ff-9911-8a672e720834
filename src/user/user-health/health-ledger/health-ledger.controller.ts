import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { HealthLedgerService } from './health-ledger.service';
import { GetHealthLedgerDataResDTO } from './dto';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiResponse,
  ApiTags,
  ApiOperation,
} from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';

import { AuthGuard } from 'src/middlewares';

@ApiTags('User-Health')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('user-health')
export class HealthLedgerController {
  constructor(private readonly healthLedgerService: HealthLedgerService) {}

  @ApiResponse({
    status: 200,
    description: 'Fetched Health Ledger Data Successfully.',
    type: GetHealthLedgerDataResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiOperation({
    summary: 'Get health ledger data for today',
  })
  @UseGuards(AuthGuard)
  @Get('health-ledger')
  async getHealthLedgerData(@Req() req: Request) {
    const user = req['user'];
    return this.healthLedgerService.getHealthLedgerData(
      user._id,
      user.timeZone,
    );
  }
}
