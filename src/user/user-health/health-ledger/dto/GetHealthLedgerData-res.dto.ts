import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export class HeartRateDTO {
  @ApiProperty({
    description: 'Minimum heart rate for today.',
    example: 60,
  })
  min: number;

  @ApiProperty({
    description: 'Maximum heart rate for today.',
    example: 100,
  })
  max: number;
}

export class GetHealthLedgerDataResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total burned calories for today.',
    example: 1000,
  })
  totalBurnedCalories: number;

  @ApiProperty({
    description: 'Total steps taken today.',
    example: 5000,
  })
  totalSteps: number;

  @ApiProperty({
    description: 'Heart rate data for today.',
    type: HeartRateDTO,
  })
  heartRate: HeartRateDTO;
}
