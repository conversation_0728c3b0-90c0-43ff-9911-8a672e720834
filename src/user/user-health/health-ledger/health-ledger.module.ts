import { Module } from '@nestjs/common';
import { HealthLedgerController } from './health-ledger.controller';
import { HealthLedgerService } from './health-ledger.service';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [CommonModule, RepoModule, AuthModule, ConfigModule],
  controllers: [HealthLedgerController],
  providers: [HealthLedgerService],
})
export class HealthLedgerModule {}
