import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import {
  AddUserHealthDataReqDTO,
  AddUserHealthDataResDTO,
  GetUserHealthDataByUserIdResDTO,
} from './dto';

import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import { User } from 'models/user';
// import { BaseResponse } from 'src/utils/responses';

@Injectable()
export class HealthDumpService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}/health`;

    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }

  async getUserHealthData(
    user: User,
  ): Promise<GetUserHealthDataByUserIdResDTO> {
    const data = await this.microserviceClient.get(
      this.apiKey,
      `${this.MICROSERVICE_URL}/${user._id}`,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
    };
  }

  async addUserHealthData(
    user: User,
    addUserHealthDTO: AddUserHealthDataReqDTO,
  ): Promise<AddUserHealthDataResDTO> {
    const data = await this.microserviceClient.post(
      this.apiKey,
      this.MICROSERVICE_URL,
      { ...addUserHealthDTO, userId: user._id },
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: data?.resp?.msg || null,
    };
  }
}
