import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { HealthDumpService } from './health-dump.service';
import {
  AddUserHealthDataReqDTO,
  AddUserHealthDataResDTO,
  GetUserHealthDataByUserIdResDTO,
} from './dto';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { Request } from 'express';

@ApiTags('User-Health')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('health-dump')
export class HealthDumpController {
  constructor(private readonly healthDumpService: HealthDumpService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user health data.',
    type: GetUserHealthDataByUserIdResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get()
  async GetUserHealthData(@Req() req: Request) {
    const user = req['user'];
    return this.healthDumpService.getUserHealthData(user);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully added the user health data.',
    type: AddUserHealthDataResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post()
  async AddUserHealthData(
    @Req() req: Request,
    @Body() addUserHealthDTO: AddUserHealthDataReqDTO,
  ) {
    const user = req['user'];

    return this.healthDumpService.addUserHealthData(user, addUserHealthDTO);
  }
}
