import { Module } from '@nestjs/common';
import { HealthDumpController } from './health-dump.controller';
import { HealthDumpService } from './health-dump.service';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [CommonModule, RepoModule, AuthModule, ConfigModule],
  controllers: [HealthDumpController],
  providers: [HealthDumpService],
})
export class HealthDumpModule {}
