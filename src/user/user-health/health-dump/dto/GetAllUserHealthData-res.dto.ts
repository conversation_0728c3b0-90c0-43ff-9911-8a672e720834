import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export class GetAllUserHealthDataResDTO extends BaseResponse {
  @ApiProperty({ description: 'Total number of records', example: 100 })
  total: number;

  @ApiProperty({
    description: 'Number of hits in the current response',
    example: 10,
  })
  nbHits: number;

  @ApiProperty({ description: 'Array of user health data', type: [Object] })
  data: Record<string, any>[];
}
