import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEnum, IsArray } from 'class-validator';

export class AddUserHealthDataReqDTO {
  @ApiProperty({
    description: 'Unique identifier of the device.',
    example: 'device-123456',
  })
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({
    description: 'Operating system of the device.',
    enum: ['android', 'ios'],
    example: 'ios',
  })
  @IsEnum(['android', 'ios'])
  os: 'android' | 'ios';

  @ApiProperty({
    description: 'Source of the health data (e.g., "apple", "google").',
    enum: ['apple', 'google'],
    example: 'apple',
  })
  @IsEnum(['apple', 'google'])
  source: 'apple' | 'google';

  @ApiProperty({
    description: 'Health data object containing key-value pairs',
    type: Object,
  })
  @IsNotEmpty()
  @IsArray()
  data: Array<Record<string, any>>;
}
