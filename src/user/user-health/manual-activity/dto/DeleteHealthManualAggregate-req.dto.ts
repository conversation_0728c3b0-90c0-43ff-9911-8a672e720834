import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class DeleteHealthManualAggregateReqDTO extends BaseResponse {
  @ApiProperty({
    description: 'Unique identifier of the user.',
    example: '609e1253c25e4a2f8c6b3d42',
  })
  @ApiProperty({ example: '609e1253c25e4a2f8c6b3d42' })
  @IsString()
  @IsNotEmpty()
  userId: string;
}
