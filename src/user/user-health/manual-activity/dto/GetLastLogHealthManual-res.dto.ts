import { ApiProperty } from '@nestjs/swagger';

export class GetLastLogHealthManualResDTO {
  @ApiProperty({ example: false })
  error: boolean;
  @ApiProperty({ example: 200 })
  statusCode: number;
  @ApiProperty({
    example: {
      source: 'appetec',
      activityType: 'Running',
      durationInMinutes: 49,
      steps: 8330,
      burnedCalories: 340,
      createdAt: '2025-05-20T12:00:00.000Z',
      updatedAt: '2025-05-20T12:30:00.000Z',
    },
  })
  data: {
    source: string;
    activityType: string;
    durationInMinutes: number;
    steps: number;
    burnedCalories: number;
    createdAt: Date;
    updatedAt: Date;
  };
}
