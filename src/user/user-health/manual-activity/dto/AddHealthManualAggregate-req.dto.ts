import { IsString, <PERSON>N<PERSON><PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum ALLOWED_ACTIVITIES {
  WALKING = 'Walking',
  RUNNING = 'Running',
  SWIMMING = 'Swimming',
  STRENGTH_TRAINING_UPPER_BODY = 'Strength Training Upper Body',
  STRENGTH_TRAINING_LOWER_BODY = 'Strength Training Lower Body',
  STRENGTH_TRAINING_FULL_BODY = 'Strength Training Full Body',
  YOGA = 'Yoga',
  DANCING = 'Dancing',
  CALISTHENICS = 'Calisthenics',
  STRETCHING = 'Stretching',
  TREKKING = 'Trekking',
}

export class AddHealthManualAggregateReqDTO {
  @ApiProperty({
    description: 'Type of activity performed.',
    enum: ALLOWED_ACTIVITIES,
    example: 'Running',
  })
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  activityType: string;

  @ApiProperty({
    description: 'Duration of the activity in minutes.',
    example: 30,
  })
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  durationInMinutes: number;
}
