// dto/edit-health-manual-aggregate-res.dto.ts

import { ApiProperty } from '@nestjs/swagger';

export class EditHealthManualAggregateResDTO {
  @ApiProperty({ example: false })
  error: boolean;

  @ApiProperty({ example: 200 })
  statusCode: number;

  @ApiProperty({ example: 'Manual activity updated successfully.' })
  msg: string;

  @ApiProperty({
    example: {
      _id: '6652bfa7c8dc12b179aadcd0',
      userId: '6652bf5dc8dc12b179aadccf',
      activityType: 'Running',
      durationInMinutes: 45,
      burnedCalories: 378,
      steps: 5400,
      createdAt: '2025-05-20T12:00:00.000Z',
      updatedAt: '2025-05-20T12:30:00.000Z',
      source: 'appetec',
    },
  })
  data: any;
}
