import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import {
  AddHealthManualAggregateReqDTO,
  AddHealthManualAggregateResDTO,
  EditHealthManualAggregateReqDTO,
  EditHealthManualAggregateResDTO,
  GetHealthManualAggregateResDTO,
  //   DeleteHealthManualAggregateReqDTO,
  DeleteHealthManualAggregateResDTO,
  GetLastLogHealthManualResDTO,
} from './dto';

import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import { User } from 'models/user';
// import { BaseResponse } from 'src/utils/responses';

@Injectable()
@Injectable()
export class ManualActivityService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}/health`;

    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }

  async addManualAggregate(
    user: User,
    dto: AddHealthManualAggregateReqDTO,
  ): Promise<AddHealthManualAggregateResDTO> {
    const userId = user._id;

    const weight = user.weight;
    const timeZone = user.timeZone;

    if (!weight || !timeZone) {
      throw new BadRequestException('User weight or time zone is missing.');
    }
    const payload = {
      ...dto,
      userId,
      weight,
      timeZone,
    };

    const data = await this.microserviceClient.post(
      this.apiKey,
      `${this.MICROSERVICE_URL}/activity-manual`,
      payload,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: data?.resp?.msg || null,
      data: data?.resp?.data || null,
    };
  }

  async editManualAggregate(
    documentId: string,
    dto: EditHealthManualAggregateReqDTO,
    user: User,
  ): Promise<EditHealthManualAggregateResDTO> {
    const payload = {
      documentId,
      userId: user._id,
      weight: user.weight,
      ...dto,
    };

    const data = await this.microserviceClient.put(
      this.apiKey,
      `${this.MICROSERVICE_URL}/edit-activity-manual/${documentId}`,
      payload,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: data?.resp?.msg || null,
      data: data?.resp?.data || {},
    };
  }

  async getManualAggregate(
    userId: string,
    timeZone: string,
  ): Promise<GetHealthManualAggregateResDTO> {
    const data = await this.microserviceClient.get(
      this.apiKey,
      `${this.MICROSERVICE_URL}/activity-manual/${userId}?timeZone=${encodeURIComponent(timeZone)}`,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message ||
          'Something went wrong while fetching activity data.',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
    };
  }

  async deleteManualAggregate(
    documentId: string,
    user: User,
    userId: string,
  ): Promise<DeleteHealthManualAggregateResDTO> {
    const payload = {
      documentId,
      userId,
    };

    const data = await this.microserviceClient.put(
      this.apiKey,
      `${this.MICROSERVICE_URL}/delete-activity-manual/${documentId}`,
      payload,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: data?.resp?.msg || null,
    };
  }

  async getManualAggregatebyActivityId(
    documentId: string,
  ): Promise<GetHealthManualAggregateResDTO> {
    const data = await this.microserviceClient.get(
      this.apiKey,
      `${this.MICROSERVICE_URL}/single-activity-manual/${documentId}`,
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message ||
          'Something went wrong while fetching activity data.',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
    };
  }

  async getLastLogActivity(user): Promise<GetLastLogHealthManualResDTO> {
    const userId = user._id;
    const timeZone = user.timeZone;

    const data = await this.microserviceClient.post(
      this.apiKey,
      `${this.MICROSERVICE_URL}/last-log-activity`,
      { userId, timeZone }, // body
    );

    if (data.error) {
      throw new BadRequestException(
        data?.resp?.data?.message ||
          'Something went wrong while fetching activity data.',
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: data?.resp?.data || null,
    };
  }
}
