import { Module } from '@nestjs/common';
import { ManualActivityService } from './manual-activity.service';
import { ManualActivityController } from './manual-activity.controller';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [CommonModule, RepoModule, AuthModule, ConfigModule],
  providers: [ManualActivityService],
  controllers: [ManualActivityController],
})
export class ManualActivityModule {}
