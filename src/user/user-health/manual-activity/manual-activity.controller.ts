import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Req,
  UseGuards,
  Param,
} from '@nestjs/common';
import { ManualActivityService } from './manual-activity.service';
import {
  AddHealthManualAggregateReqDTO,
  EditHealthManualAggregateReqDTO,
  EditHealthManualAggregateResDTO,
  GetHealthManualAggregateResDTO,
} from './dto';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
// import { ErrorResponse } from 'src/utils/responses';
import { Request } from 'express';

@ApiTags('User-Health')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('user-health')
export class ManualActivityController {
  constructor(private readonly manualActivityService: ManualActivityService) {}
  @Post('activity-manual')
  async addManualAggregate(
    @Req() req: Request,
    @Body() body: AddHealthManualAggregateReqDTO,
  ) {
    const user = req['user'];
    return this.manualActivityService.addManualAggregate(user, body);
  }

  @Put('edit-activity-manual/:documentId')
  async editManualActivity(
    @Req() req: Request,
    @Param('documentId') documentId: string,
    @Body() body: EditHealthManualAggregateReqDTO,
  ): Promise<EditHealthManualAggregateResDTO> {
    const user = req['user'];
    return this.manualActivityService.editManualAggregate(
      documentId,
      body,
      user,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved manual activity data.',
    type: GetHealthManualAggregateResDTO,
  })
  @UseGuards(AuthGuard)
  @Get('activity-manual')
  async getManualActivity(@Req() req: Request) {
    const user = req['user'];
    return this.manualActivityService.getManualAggregate(
      user._id,
      user.timeZone,
    );
  }
  @Put('delete-activity-manual/:activityId')
  async deleteManualActivity(
    @Req() req: Request,
    @Param('activityId') documentId: string,
  ) {
    const user = req['user'];
    return this.manualActivityService.deleteManualAggregate(
      documentId,
      user,
      user._id,
    );
  }

  @Get('single-activity-manual/:activityId')
  async getManualbyActivityId(@Param('activityId') documentId: string) {
    return this.manualActivityService.getManualAggregatebyActivityId(
      documentId,
    );
  }
  @UseGuards(AuthGuard)
  @Get('last-log-activity')
  async getLastLogActivity(@Req() req: Request) {
    const user = req['user'];
    return this.manualActivityService.getLastLogActivity(user);
  }
}
