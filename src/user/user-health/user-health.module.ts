import { Module } from '@nestjs/common';
import { HealthDumpService } from './health-dump/health-dump.service';
import { HealthDumpController } from './health-dump/health-dump.controller';
import { ManualActivityService } from './manual-activity/manual-activity.service';
import { ManualActivityController } from './manual-activity/manual-activity.controller';
import { HealthDumpModule } from './health-dump/health-dump.module';
import { HealthLedgerModule } from './health-ledger/health-ledger.module';

import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { ManualActivityModule } from './manual-activity/manual-activity.module';
import { SystemActivityModule } from './system-activity/system-activity.module';

import { HealthHighlightModule } from './health-highlight/health-highlight.module';
import { HealthAnalyticsModule } from './health-analytics/health-analytics.module';
import { AverageHealthDetailModule } from './average-health-detail/average-health-detail.module';
import { AverageActivityDetailModule } from './average-activity-detail/average-activity-detail.module';

@Module({
  imports: [
    HealthDumpModule,
    ManualActivityModule,
    CommonModule,
    RepoModule,
    AuthModule,
    ConfigModule,
    SystemActivityModule,
    HealthLedgerModule,
    HealthHighlightModule,
    HealthAnalyticsModule,
    AverageHealthDetailModule,
    AverageActivityDetailModule,
  ],
  providers: [ManualActivityService, HealthDumpService],
  controllers: [ManualActivityController, HealthDumpController],
})
export class UserHealthModule {}
