import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { NotificationTokenDto } from './notificationToken';

export class CreateNotificationTokenResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message indicating the status of the push token creation',
    example: 'Push token created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'The push token data returned after creation',
    type: NotificationTokenDto,
  })
  data: NotificationTokenDto;
}
