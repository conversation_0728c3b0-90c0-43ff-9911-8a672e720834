import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsBoolean, IsString } from 'class-validator';
import { BaseResponse } from 'src/utils/responses';
import { NotificationTokenDto } from './notificationToken';

export class ToggleNotificationStatusReqDTO {
  @ApiProperty({ description: "Unique identifier for the user's device" })
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({
    description: 'Indicates whether notifications are enabled or disabled',
  })
  @IsBoolean()
  @IsNotEmpty()
  isNotificationActive: boolean;
}

export class ToggleNotificationStatusResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message indicating the status of the update operation',
    example: 'Notification status updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'The updated notification token data',
  })
  data: NotificationTokenDto;
}
