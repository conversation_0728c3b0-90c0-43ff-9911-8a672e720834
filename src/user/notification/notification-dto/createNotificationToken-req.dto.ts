import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateNotificationTokenReqDTO {
  @ApiProperty({ description: "Unique identifier for the user's device" })
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({ description: 'Notification token for push notifications' })
  @IsString()
  @IsNotEmpty()
  notificationToken: string;

  @ApiProperty({
    description: "Operating system of the user's device",
    example: ['android', 'ios', 'windows'],
  })
  @IsString()
  @IsNotEmpty()
  osType: string;

  @ApiProperty({
    description: "Type of the user's device",
    example: ['mobile', 'tablet', 'ipad'],
  })
  @IsString()
  @IsNotEmpty()
  deviceType: string;
}
