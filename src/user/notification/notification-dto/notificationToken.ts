import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';
import { NotificationToken } from 'models/notificationToken';

export class NotificationTokenDto {
  @ApiProperty({ description: 'Unique identifier for the user' })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'Unique identifier for the device' })
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({ description: 'Notification token for push notifications' })
  @IsString()
  @IsNotEmpty()
  notificationToken: string;

  @ApiProperty({ description: 'Indicates if notifications are active' })
  @IsBoolean()
  @IsNotEmpty()
  isNotificationActive: boolean;

  @ApiProperty({ description: 'Indicates if the token is active' })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @ApiProperty({
    description: 'Operating system of the device',
    enum: ['android', 'ios', 'windows'],
  })
  @IsString()
  @IsNotEmpty()
  osType: string;

  @ApiProperty({
    description: 'Type of the device',
    enum: ['mobile', 'tablet', 'ipad'],
  })
  @IsString()
  @IsNotEmpty()
  deviceType: string;

  static transform(object: NotificationToken): NotificationTokenDto {
    const transformedObj = new NotificationTokenDto();
    transformedObj.userId = object.userId;
    transformedObj.deviceId = object.deviceId;
    transformedObj.notificationToken = object.notificationToken;
    transformedObj.isNotificationActive = object.isNotificationActive;
    transformedObj.deviceType = object.deviceType;
    transformedObj.osType = object.osType;
    transformedObj.isActive = object.isActive;

    return transformedObj;
  }
}
