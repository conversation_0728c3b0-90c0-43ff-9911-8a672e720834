import {
  Body,
  Controller,
  Post,
  Req,
  UseGuards,
  Put,
  Param,
  Get,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { ErrorResponse } from 'src/utils/responses';
import {
  CreateNotificationTokenReqDTO,
  CreateNotificationTokenResDTO,
  GetUserNotificationTokensResDTO,
  ToggleNotificationStatusReqDTO,
  ToggleNotificationStatusResDTO,
} from './notification-dto';
import { NotificationService } from './notificationToken.service';
import { Request } from 'express';

@ApiTags('Notification-Token')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('notification_token')
export class NotificationTokenController {
  constructor(private readonly notificationTokenService: NotificationService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully created the notification token.',
    type: CreateNotificationTokenResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post()
  async createNotificationToken(
    @Req() req: Request,
    @Body() notificationTokenData: CreateNotificationTokenReqDTO,
  ) {
    const user = req['user'];

    return this.notificationTokenService.createUserNotificationToken(
      user,
      notificationTokenData,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Toggle user notification token data.',
    type: ToggleNotificationStatusResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put()
  async toggleNotificationStatus(
    @Req() req: Request,
    @Body() toggleStatusDto: ToggleNotificationStatusReqDTO,
  ) {
    const user = req['user'];

    return this.notificationTokenService.toggleNotificationStatus(
      user,
      toggleStatusDto,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Get user device notification token data.',
    type: GetUserNotificationTokensResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/:deviceId')
  async GetUserNotificationTokensResDTO(
    @Req() req: Request,
    @Param('deviceId') deviceId: string,
  ) {
    const user = req['user'];

    return this.notificationTokenService.getUserNotificationTokens(
      user,
      deviceId,
    );
  }
}
