import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { User } from 'models/user';
import {
  NotificationCounter,
  NotificationToken,
} from 'models/notificationToken';
import {
  CreateNotificationTokenReqDTO,
  GetUserNotificationTokensResDTO,
  ToggleNotificationStatusReqDTO,
  ToggleNotificationStatusResDTO,
} from './notification-dto';
import { CreateNotificationTokenResDTO } from './notification-dto';
import { NotificationTokenDto } from './notification-dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(NotificationToken.name)
    private readonly notificationTokenModel: Model<NotificationToken>,

    @InjectModel(NotificationCounter.name)
    private readonly notificationCounterModel: Model<NotificationCounter>,
  ) {}

  async createUserNotificationToken(
    user: User,
    notificationTokenData: CreateNotificationTokenReqDTO,
  ): Promise<CreateNotificationTokenResDTO> {
    const { deviceId, deviceType, notificationToken, osType } =
      notificationTokenData;

    // Run both queries in parallel
    const [previousUserToken, existingNotificationToken] = await Promise.all([
      this.notificationTokenModel.findOne({
        deviceId,
        userId: { $ne: user._id },
      }),
      this.notificationTokenModel.findOne({
        userId: user._id,
        deviceId,
        notificationToken,
      }),
    ]);

    // Deactivate previous user's token if exists (no need to await if not needed for logic)
    if (previousUserToken) {
      this.notificationTokenModel.updateOne(
        { _id: previousUserToken._id },
        { isActive: false, isNotificationActive: false },
      );
    }

    let tokenDoc;

    if (!existingNotificationToken) {
      // Create new token and counter in parallel
      tokenDoc = await this.notificationTokenModel.create({
        deviceId,
        deviceType,
        notificationToken,
        osType,
        userId: user._id,
      });

      this.notificationCounterModel.create({
        deviceId,
        userId: user._id,
        notificationTokenId: tokenDoc._id,
      }); // Fire and forget

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'New Notification Token created successfully!',
        data: NotificationTokenDto.transform(tokenDoc),
      };
    } else {
      // Update token and counter in parallel
      await Promise.all([
        this.notificationTokenModel.updateOne(
          { _id: existingNotificationToken._id },
          { notificationToken, isActive: true, isNotificationActive: true },
        ),

        this.notificationCounterModel.updateOne(
          {
            deviceId,
            userId: user._id,
            notificationTokenId: existingNotificationToken._id,
          },
          { counter: 0 },
          { upsert: true },
        ),
      ]);

      // No need to mutate existingNotificationToken, just update the fields for the response
      tokenDoc = {
        ...existingNotificationToken.toObject(),
        notificationToken,
        isActive: true,
        isNotificationActive: true,
      };

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Notification Token Updated Successfully!',
        data: NotificationTokenDto.transform(tokenDoc),
      };
    }
  }

  async toggleNotificationStatus(
    user: User,
    toggleStatusDto: ToggleNotificationStatusReqDTO,
  ): Promise<ToggleNotificationStatusResDTO> {
    const { deviceId, isNotificationActive } = toggleStatusDto;

    const userNotificationTokenData = await this.notificationTokenModel.findOne(
      {
        userId: user._id,
        deviceId,
      },
    );

    if (!userNotificationTokenData) {
      throw new NotFoundException('Notification token not found !!.');
    }

    // Update the isNotificationActive status
    userNotificationTokenData.isNotificationActive = isNotificationActive;
    userNotificationTokenData.isActive = true;
    await userNotificationTokenData.save();

    const resp = NotificationTokenDto.transform(userNotificationTokenData);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Notification status updated successfully',
      data: resp,
    };
  }

  async getUserNotificationTokens(
    user: User,
    deviceId: string,
  ): Promise<GetUserNotificationTokensResDTO> {
    const userNotificationTokenData = await this.notificationTokenModel.findOne(
      {
        userId: user._id,
        deviceId,
      },
    );

    if (!userNotificationTokenData) {
      throw new NotFoundException('Notification token not found !!.');
    }

    const resp = NotificationTokenDto.transform(userNotificationTokenData);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
