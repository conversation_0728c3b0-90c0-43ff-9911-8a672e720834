import { Modu<PERSON> } from '@nestjs/common';
import { NotificationService } from './notificationToken.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  NotificationCounter,
  NotificationCounterSchema,
  NotificationToken,
  NotificationTokenSchema,
} from 'models/notificationToken';
import { User, UserSchema } from 'models/user';
import { NotificationTokenController } from './notificationToken.controller';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';
import { RemindersModule } from '../reminders/reminders.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: NotificationToken.name, schema: NotificationTokenSchema },
      { name: NotificationCounter.name, schema: NotificationCounterSchema },
    ]),
    CommonModule,
    AuthModule,
    RepoModule,
    RemindersModule,
    ThirdPartyModule,
  ],
  providers: [NotificationService],
  controllers: [NotificationTokenController],
  exports: [MongooseModule],
})
export class NotificationTokenModule {}
