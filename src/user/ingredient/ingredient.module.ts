import { Module } from '@nestjs/common';
import { IngredientService } from './ingredient.service';
import { IngredientController } from './ingredient.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Ingredient } from 'models/ingredient';
import { IngredientSchema } from 'models/ingredient/ingredient.schema';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Ingredient.name, schema: IngredientSchema },
    ]),
    AuthModule,
    RepoModule,
    CommonModule,
    ThirdPartyModule,
  ],
  controllers: [IngredientController],
  providers: [IngredientService],
})
export class IngredientModule {}
