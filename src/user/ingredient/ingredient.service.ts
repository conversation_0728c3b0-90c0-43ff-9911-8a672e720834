import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Ingredient } from 'models/ingredient/ingredient.schema';
import { isValidObjectId, Model } from 'mongoose';
import {
  GetAllIngredientResDTO,
  GetSingleIngredientResDTO,
  IngredientDTO,
} from './dto';
import { getAllIngredientQueryInterface } from './interface';
import { UtilsService } from 'src/common/services';

@Injectable()
export class IngredientService {
  constructor(
    @InjectModel(Ingredient.name)
    private readonly ingredientModel: Model<Ingredient>,
    private readonly utilsService: UtilsService,
  ) {}

  async getAllIngredients(
    queryFilters: getAllIngredientQueryInterface,
  ): Promise<GetAllIngredientResDTO> {
    const { page, name } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    if (name) {
      query.name = { $regex: new RegExp(name, 'i') };
    }

    const ingredients = await this.ingredientModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.ingredientModel.countDocuments(query);

    const ingredientResp = ingredients.map((item) =>
      IngredientDTO.transform(item),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: ingredientResp.length,
      data: ingredientResp,
    };
  }

  async getSingleIngredient(id: string): Promise<GetSingleIngredientResDTO> {
    if (!isValidObjectId(id)) {
      throw new BadRequestException('Invalid ingredient ID format');
    }

    const ingredient = await this.ingredientModel.findById(id).exec();

    if (!ingredient || ingredient.isDeleted) {
      throw new NotFoundException('Ingredient not found');
    }

    const resp = IngredientDTO.transform(ingredient);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
