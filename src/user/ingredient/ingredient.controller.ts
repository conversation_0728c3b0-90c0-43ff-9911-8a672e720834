import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { IngredientService } from './ingredient.service';
import { AuthGuard } from 'src/middlewares';
import { getAllIngredientQueryInterface } from './interface';

@UseGuards(AuthGuard)
@Controller('ingredient')
export class IngredientController {
  constructor(private readonly ingredientService: IngredientService) {}

  @Get()
  async getAllIngredients(
    @Query() filterQueries: getAllIngredientQueryInterface,
  ) {
    return this.ingredientService.getAllIngredients(filterQueries);
  }

  @Get('/:ingredientId')
  async getSingleIngredient(@Param('ingredientId') id: string) {
    return this.ingredientService.getSingleIngredient(id);
  }
}
